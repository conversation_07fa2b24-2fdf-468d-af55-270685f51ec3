import { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    optimizePackageImports: [
      '@thf/ui',
      '@thf/teams-ui',
      '@thf/icons',
      'lucide-react',
      'framer-motion',
    ],
  },
  
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      config.optimization.splitChunks.chunks = 'all';
      config.optimization.splitChunks.cacheGroups = {
        ...config.optimization.splitChunks.cacheGroups,
        teams: {
          name: 'teams',
          test: /[\\/]packages[\\/]teams-/,
          priority: 30,
          reuseExistingChunk: true,
        },
        ui: {
          name: 'ui',
          test: /[\\/]packages[\\/](ui|icons|animations)[\\/]/,
          priority: 20,
          reuseExistingChunk: true,
        },
      };
    }
    return config;
  },
  
  images: {
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
  },

  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
      },
    ];
  },
};

export default nextConfig;
