{"name": "teams", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@hookform/resolvers": "^5.1.1", "react-hook-form": "^7.58.1", "zod": "^3.25.67", "framer-motion": "^12.19.1", "date-fns": "^4.1.0", "lucide-react": "^0.522.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "@supabase/supabase-js": "^2.50.0", "@supabase/ssr": "^0.6.1", "@react-email/components": "^0.1.0", "@react-email/render": "^1.1.2", "resend": "^4.6.0", "zustand": "^5.0.5"}, "devDependencies": {"eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}}