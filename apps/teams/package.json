{"name": "teams", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.1.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@tanstack/react-query": "^5.59.0", "@tanstack/react-query-devtools": "^5.59.0", "@hookform/resolvers": "^3.9.0", "react-hook-form": "^7.53.0", "zod": "^3.23.8", "framer-motion": "^11.11.0", "date-fns": "^3.6.0", "lucide-react": "^0.446.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.2", "@supabase/supabase-js": "^2.45.4", "@supabase/ssr": "^0.5.1", "@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "resend": "^4.0.0", "zustand": "^4.4.7"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-next": "^15.1.0", "@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.0"}}