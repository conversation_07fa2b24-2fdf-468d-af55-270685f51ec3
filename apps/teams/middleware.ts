import { createAuthMiddleware } from '@thf/teams-auth/middleware/auth-middleware';

export const middleware = createAuthMiddleware({
  publicRoutes: ['/login', '/signup', '/forgot-password'],
  protectedRoutes: ['/dashboard', '/applications', '/members', '/projects', '/proposals', '/nda'],
  adminRoutes: ['/settings'],
  redirectTo: '/dashboard',
  loginPath: '/login',
});

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
