export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      JOIN_US_TABLE: {
        Row: {
          id: string
          identifier: string
          full_name: string
          email: string
          role: Database['public']['Enums']['Role Types']
          status: 'received' | 'reviewed' | 'accepted' | 'rejected'
          priority: 'urgent' | 'high' | 'medium' | 'low' | 'no-priority' | null
          experience: string
          skills: string[]
          portfolio_url: string | null
          motivation: string
          availability: 'full-time' | 'part-time' | 'project-based'
          is_nda_signed: boolean | null
          nda_signed_date: string | null
          reviewed_by: string | null
          reviewed_at: string | null
          notes: string | null
          created_at: string
          updated_at: string
          phone: string | null
          location: string | null
          linkedin_url: string | null
          github_url: string | null
          years_of_experience: number | null
          expected_start_date: string | null
        }
        Insert: {
          id?: string
          identifier?: string
          full_name: string
          email: string
          role: Database['public']['Enums']['Role Types']
          status?: 'received' | 'reviewed' | 'accepted' | 'rejected'
          priority?: 'urgent' | 'high' | 'medium' | 'low' | 'no-priority' | null
          experience: string
          skills: string[]
          portfolio_url?: string | null
          motivation: string
          availability: 'full-time' | 'part-time' | 'project-based'
          is_nda_signed?: boolean | null
          nda_signed_date?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
          phone?: string | null
          location?: string | null
          linkedin_url?: string | null
          github_url?: string | null
          years_of_experience?: number | null
          expected_start_date?: string | null
        }
        Update: {
          id?: string
          identifier?: string
          full_name?: string
          email?: string
          role?: Database['public']['Enums']['Role Types']
          status?: 'received' | 'reviewed' | 'accepted' | 'rejected'
          priority?: 'urgent' | 'high' | 'medium' | 'low' | 'no-priority' | null
          experience?: string
          skills?: string[]
          portfolio_url?: string | null
          motivation?: string
          availability?: 'full-time' | 'part-time' | 'project-based'
          is_nda_signed?: boolean | null
          nda_signed_date?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
          phone?: string | null
          location?: string | null
          linkedin_url?: string | null
          github_url?: string | null
          years_of_experience?: number | null
          expected_start_date?: string | null
        }
      }
      profiles: {
        Row: {
          id: string
          full_name: string
          email: string
          role: Database['public']['Enums']['Role Types']
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name: string
          email: string
          role: Database['public']['Enums']['Role Types']
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          email?: string
          role?: Database['public']['Enums']['Role Types']
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      waitlists: {
        Row: {
          id: string
          email: string
          full_name: string | null
          role_interest: Database['public']['Enums']['Role Types'] | null
          source: string | null
          utm_campaign: string | null
          utm_source: string | null
          utm_medium: string | null
          is_launch_day_sent: boolean
          launch_day_sent_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string | null
          role_interest?: Database['public']['Enums']['Role Types'] | null
          source?: string | null
          utm_campaign?: string | null
          utm_source?: string | null
          utm_medium?: string | null
          is_launch_day_sent?: boolean
          launch_day_sent_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          role_interest?: Database['public']['Enums']['Role Types'] | null
          source?: string | null
          utm_campaign?: string | null
          utm_source?: string | null
          utm_medium?: string | null
          is_launch_day_sent?: boolean
          launch_day_sent_at?: string | null
          created_at?: string
        }
      }
      Emails: {
        Row: {
          id: string
          recipient_email: string
          recipient_name: string | null
          template_name: string
          subject: string
          status: 'pending' | 'sent' | 'delivered' | 'failed'
          sent_at: string | null
          delivered_at: string | null
          error_message: string | null
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          recipient_email: string
          recipient_name?: string | null
          template_name: string
          subject: string
          status?: 'pending' | 'sent' | 'delivered' | 'failed'
          sent_at?: string | null
          delivered_at?: string | null
          error_message?: string | null
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          recipient_email?: string
          recipient_name?: string | null
          template_name?: string
          subject?: string
          status?: 'pending' | 'sent' | 'delivered' | 'failed'
          sent_at?: string | null
          delivered_at?: string | null
          error_message?: string | null
          metadata?: Json | null
          created_at?: string
        }
      }
    }
    Enums: {
      'Role Types': 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
    }
  }
}

export type TeamApplication = Database['public']['Tables']['JOIN_US_TABLE']['Row']
export type TeamProfile = Database['public']['Tables']['profiles']['Row']
export type WaitlistEntry = Database['public']['Tables']['waitlists']['Row']
export type EmailLog = Database['public']['Tables']['Emails']['Row']
