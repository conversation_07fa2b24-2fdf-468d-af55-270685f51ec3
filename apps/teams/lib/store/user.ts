import { User } from '@supabase/supabase-js';
import { create } from 'zustand';
import { Database, TeamProfile } from '../supabase/types';

type UserType = User | null;
type ProfileType = TeamProfile | null;
type RoleType = Database['public']['Enums']['Role Types'] | null;

type State = {
  user: UserType;
  profile: ProfileType;
  role: RoleType;
};

type Action = {
  updateUser: (user: State['user']) => void;
  removeUser: () => void;
  updateRole: (role: State['role']) => void;
  removeRole: () => void;
  updateProfile: (profile: State['profile']) => void;
  removeProfile: () => void;
};

export const userStore = create<State & Action>((set) => ({
  user: null,
  profile: null,
  role: null,
  updateUser: (user) => set(() => ({ user: user })),
  removeUser: () => set(() => ({ user: null })),
  updateRole: (role) => set(() => ({ role: role })),
  removeRole: () => set(() => ({ role: null })),
  updateProfile: (profile) => set(() => ({ profile: profile })),
  removeProfile: () => set(() => ({ profile: null })),
}));
