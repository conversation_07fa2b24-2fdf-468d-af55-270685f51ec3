import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

// Simple server-side Supabase client
async function createClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );
}

// Simple auth check
async function checkAuth() {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();
  return session;
}

// Type definition
interface CreateApplicationData {
  full_name: string;
  email: string;
  role: string;
  experience: string;
  skills: string[];
  motivation: string;
  availability: string;
  portfolio_url?: string;
  phone?: string;
  location?: string;
  linkedin_url?: string;
  github_url?: string;
  years_of_experience?: number;
}

/**
 * GET /api/applications
 * Fetch applications with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await checkAuth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const role = searchParams.get('role');
    const search = searchParams.get('search');

    const supabase = await createClient();
    let query = supabase.from('JOIN_US_TABLE').select('*', { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (role) {
      query = query.eq('role', role);
    }
    if (search) {
      query = query.or(
        `full_name.ilike.%${search}%,email.ilike.%${search}%,identifier.ilike.%${search}%`
      );
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Apply sorting
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: data || [],
      count: count || 0,
      page,
      limit,
      totalPages,
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/applications
 * Create a new application
 */
export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as CreateApplicationData;

    // Validate required fields
    const requiredFields = [
      'full_name',
      'email',
      'role',
      'experience',
      'skills',
      'motivation',
      'availability',
    ];
    const missingFields = requiredFields.filter(
      (field) => !body[field as keyof CreateApplicationData]
    );

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Generate unique identifier
    const supabase = await createClient();
    const { count } = await supabase
      .from('JOIN_US_TABLE')
      .select('*', { count: 'exact', head: true });

    const identifier = `APP-${((count || 0) + 1).toString().padStart(4, '0')}`;

    // Create application
    const applicationData = {
      ...body,
      identifier,
      status: 'received' as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data: newApplication, error } = await supabase
      .from('JOIN_US_TABLE')
      .insert(applicationData)
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to create application' },
        { status: 500 }
      );
    }

    // TODO: Send confirmation email
    console.log('Application created:', newApplication.identifier);

    return NextResponse.json(newApplication, { status: 201 });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
