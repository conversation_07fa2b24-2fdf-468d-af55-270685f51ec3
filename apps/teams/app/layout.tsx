import { QueryProvider } from '@/components/providers/query-provider';
import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'Teams - The Hue Factory',
  description: 'Team management and collaboration platform for The Hue Factory',
  icons: {
    icon: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body className='min-h-screen bg-gray-50 font-sans antialiased'>
        <QueryProvider>{children}</QueryProvider>
      </body>
    </html>
  );
}
