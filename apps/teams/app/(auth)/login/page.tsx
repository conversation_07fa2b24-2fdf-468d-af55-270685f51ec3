'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirectTo') || '/dashboard';

  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      setError(null);

      const { supabaseClient } = await import('@/lib/supabase/client');
      const supabase = supabaseClient();

      const { error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (error) {
        setError(error.message);
        return;
      }

      router.push(redirectTo);
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    }
  };

  return (
    <div className='min-h-screen flex items-center justify-center bg-background px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8'>
        {/* Header */}
        <div className='text-center'>
          <div className='mx-auto h-12 w-12 bg-primary rounded-lg flex items-center justify-center mb-6'>
            <span className='text-primary-foreground font-bold text-xl'>T</span>
          </div>

          <h2 className='text-3xl font-bold text-foreground'>Welcome back</h2>
          <p className='mt-2 text-sm text-muted-foreground'>
            Sign in to your teams account
          </p>
        </div>

        {/* Form */}
        <form className='mt-8 space-y-6' onSubmit={handleSubmit(onSubmit)}>
          {error && (
            <div className='bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md text-sm'>
              {error}
            </div>
          )}

          <div className='space-y-4'>
            {/* Email Field */}
            <div>
              <label
                htmlFor='email'
                className='block text-sm font-medium text-foreground mb-2'
              >
                Email address
              </label>
              <input
                {...register('email')}
                type='email'
                autoComplete='email'
                className='form-input'
                placeholder='Enter your email'
              />
              {errors.email && (
                <p className='mt-1 text-sm text-destructive'>
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label
                htmlFor='password'
                className='block text-sm font-medium text-foreground mb-2'
              >
                Password
              </label>
              <div className='relative'>
                <input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete='current-password'
                  className='form-input pr-10'
                  placeholder='Enter your password'
                />
                <button
                  type='button'
                  className='absolute inset-y-0 right-0 pr-3 flex items-center'
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
              {errors.password && (
                <p className='mt-1 text-sm text-destructive'>
                  {errors.password.message}
                </p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <button
            type='submit'
            disabled={isSubmitting}
            className='w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
          >
            {isSubmitting ? 'Signing in...' : 'Sign in'}
          </button>

          {/* Links */}
          <div className='flex items-center justify-between text-sm'>
            <a
              href='/forgot-password'
              className='text-primary hover:text-primary/80 transition-colors'
            >
              Forgot your password?
            </a>
            <a
              href='/signup'
              className='text-primary hover:text-primary/80 transition-colors'
            >
              Create account
            </a>
          </div>
        </form>

        {/* Footer */}
        <div className='text-center'>
          <p className='text-xs text-muted-foreground'>
            © 2024 The Hue Factory. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
