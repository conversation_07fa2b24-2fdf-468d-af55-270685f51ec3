'use client';

import { useGetUser } from '@/lib/hooks/useGetUser';
import { supabaseClient } from '@/lib/supabase/client';
import { TeamApplication } from '@/lib/supabase/types';
import { useEffect, useState } from 'react';

export default function ApplicationsPage() {
  const { role } = useGetUser();
  const [applications, setApplications] = useState<TeamApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<
    TeamApplication[]
  >([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');

  useEffect(() => {
    const supabase = supabaseClient();

    // Only admins can view all applications
    if (role === 'Admin') {
      supabase
        .from('JOIN_US_TABLE')
        .select('*')
        .order('created_at', { ascending: false })
        .then(({ data }) => {
          if (data) {
            setApplications(data);
            setFilteredApplications(data);
          }
        });

      // Set up real-time subscription
      const channel = supabase
        .channel('applications-page')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
          (payload) => {
            setApplications((current) => {
              switch (payload.eventType) {
                case 'INSERT':
                  return [payload.new as TeamApplication, ...current];
                case 'UPDATE':
                  return current.map((app) =>
                    app.id === payload.new.id
                      ? (payload.new as TeamApplication)
                      : app
                  );
                case 'DELETE':
                  return current.filter((app) => app.id !== payload.old.id);
                default:
                  return current;
              }
            });
          }
        )
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    }
  }, [role]);

  // Filter applications based on status and role
  useEffect(() => {
    let filtered = applications;

    if (statusFilter !== 'all') {
      filtered = filtered.filter((app) => app.status === statusFilter);
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter((app) => app.role === roleFilter);
    }

    setFilteredApplications(filtered);
  }, [applications, statusFilter, roleFilter]);

  // Calculate statistics
  const stats = {
    total: applications.length,
    pending: applications.filter((app) => app.status === 'received').length,
    approved: applications.filter((app) => app.status === 'accepted').length,
    rejected: applications.filter((app) => app.status === 'rejected').length,
    thisMonth: applications.filter((app) => {
      const createdAt = new Date(app.created_at);
      const now = new Date();
      return (
        createdAt.getMonth() === now.getMonth() &&
        createdAt.getFullYear() === now.getFullYear()
      );
    }).length,
  };

  if (role !== 'Admin') {
    return (
      <div className='p-6'>
        <div className='bg-card border border-border rounded-lg p-6'>
          <h3 className='text-lg font-semibold mb-4'>Access Denied</h3>
          <p className='text-muted-foreground'>
            You don't have permission to view applications. Only admins can
            access this page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6'>
      <div className='mb-6'>
        <h1 className='text-2xl font-bold text-foreground'>Applications</h1>
        <p className='text-muted-foreground'>
          {filteredApplications.length} of {applications.length} applications
        </p>
      </div>

      {/* Statistics Cards */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4 mb-6'>
        <div className='p-4 bg-card border border-border rounded-lg'>
          <h4 className='font-medium text-sm mb-2'>Total Applications</h4>
          <p className='text-2xl font-bold'>{stats.total}</p>
          <p className='text-xs text-muted-foreground'>All time</p>
        </div>
        <div className='p-4 bg-card border border-border rounded-lg'>
          <h4 className='font-medium text-sm mb-2'>Pending Review</h4>
          <p className='text-2xl font-bold text-yellow-600'>{stats.pending}</p>
          <p className='text-xs text-muted-foreground'>Awaiting review</p>
        </div>
        <div className='p-4 bg-card border border-border rounded-lg'>
          <h4 className='font-medium text-sm mb-2'>Approved</h4>
          <p className='text-2xl font-bold text-green-600'>{stats.approved}</p>
          <p className='text-xs text-muted-foreground'>Team members</p>
        </div>
        <div className='p-4 bg-card border border-border rounded-lg'>
          <h4 className='font-medium text-sm mb-2'>This Month</h4>
          <p className='text-2xl font-bold'>{stats.thisMonth}</p>
          <p className='text-xs text-muted-foreground'>New applications</p>
        </div>
      </div>

      {/* Filters */}
      <div className='flex gap-4 mb-6'>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className='form-select'
        >
          <option value='all'>All Statuses</option>
          <option value='received'>Pending</option>
          <option value='reviewed'>Under Review</option>
          <option value='accepted'>Approved</option>
          <option value='rejected'>Rejected</option>
        </select>

        <select
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value)}
          className='form-select'
        >
          <option value='all'>All Roles</option>
          <option value='Admin'>Admin</option>
          <option value='Collaborator'>Collaborator</option>
          <option value='Affiliate'>Affiliate</option>
          <option value='Volunteer'>Volunteer</option>
        </select>
      </div>

      {/* Applications List */}
      <div className='bg-card border border-border rounded-lg'>
        <div className='p-4 border-b border-border'>
          <h3 className='text-lg font-semibold'>Applications</h3>
        </div>
        <div className='divide-y divide-border'>
          {filteredApplications.length > 0 ? (
            filteredApplications.map((application) => (
              <ApplicationRow key={application.id} application={application} />
            ))
          ) : (
            <div className='p-8 text-center text-muted-foreground'>
              No applications found matching your filters.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Application Row Component
interface ApplicationRowProps {
  application: TeamApplication;
}

function ApplicationRow({ application }: ApplicationRowProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  const updateStatus = async (newStatus: string) => {
    setIsUpdating(true);
    const supabase = supabaseClient();

    await supabase
      .from('JOIN_US_TABLE')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString(),
        reviewed_at: new Date().toISOString(),
      })
      .eq('id', application.id);

    setIsUpdating(false);
  };

  const statusConfig = {
    received: { label: 'Pending', className: 'bg-yellow-100 text-yellow-800' },
    reviewed: { label: 'Under Review', className: 'bg-blue-100 text-blue-800' },
    accepted: { label: 'Approved', className: 'bg-green-100 text-green-800' },
    rejected: { label: 'Rejected', className: 'bg-red-100 text-red-800' },
  };

  const config =
    statusConfig[application.status as keyof typeof statusConfig] ||
    statusConfig.received;

  return (
    <div className='p-4 hover:bg-muted/50 transition-colors'>
      <div className='flex items-center justify-between'>
        <div className='flex-1'>
          <div className='flex items-center gap-3 mb-2'>
            <h4 className='font-medium'>{application.full_name}</h4>
            <span
              className={`text-xs px-2 py-1 rounded-full ${config.className}`}
            >
              {config.label}
            </span>
            {application.priority && application.priority !== 'no-priority' && (
              <span className='text-xs px-2 py-1 rounded-full bg-orange-100 text-orange-800'>
                {application.priority}
              </span>
            )}
          </div>
          <div className='text-sm text-muted-foreground space-y-1'>
            <p>
              <strong>Email:</strong> {application.email}
            </p>
            <p>
              <strong>Role:</strong> {application.role}
            </p>
            <p>
              <strong>ID:</strong> {application.identifier}
            </p>
            <p>
              <strong>Applied:</strong>{' '}
              {new Date(application.created_at).toLocaleDateString()}
            </p>
            {application.skills && application.skills.length > 0 && (
              <p>
                <strong>Skills:</strong> {application.skills.join(', ')}
              </p>
            )}
          </div>
        </div>

        <div className='flex gap-2'>
          {application.status === 'received' && (
            <>
              <button
                onClick={() => updateStatus('accepted')}
                disabled={isUpdating}
                className='px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50'
              >
                Approve
              </button>
              <button
                onClick={() => updateStatus('rejected')}
                disabled={isUpdating}
                className='px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50'
              >
                Reject
              </button>
            </>
          )}
          <a
            href={`/applications/${application.id}`}
            className='px-3 py-1 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90'
          >
            View
          </a>
        </div>
      </div>
    </div>
  );
}
