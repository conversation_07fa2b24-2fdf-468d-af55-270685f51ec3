export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='min-h-screen bg-background'>
      <div className='flex'>
        {/* Sidebar placeholder */}
        <div className='w-64 bg-sidebar border-r border-sidebar-border'>
          <div className='p-4'>
            <h2 className='text-lg font-semibold'>Teams</h2>
            <nav className='mt-4 space-y-2'>
              <a
                href='/dashboard'
                className='block px-3 py-2 rounded-md hover:bg-sidebar-accent'
              >
                Dashboard
              </a>
              <a
                href='/applications'
                className='block px-3 py-2 rounded-md hover:bg-sidebar-accent'
              >
                Applications
              </a>
            </nav>
          </div>
        </div>

        {/* Main content */}
        <div className='flex-1'>{children}</div>
      </div>
    </div>
  );
}
