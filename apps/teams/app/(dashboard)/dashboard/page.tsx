'use client';

import { useGetUser } from '@/lib/hooks/useGetUser';
import { supabaseClient } from '@/lib/supabase/client';
import { TeamApplication, WaitlistEntry } from '@/lib/supabase/types';
import { useEffect, useState } from 'react';

export default function DashboardPage() {
  const { profile, role } = useGetUser();
  const [applications, setApplications] = useState<TeamApplication[]>([]);
  const [waitlist, setWaitlist] = useState<WaitlistEntry[]>([]);
  const [currentUserApplication, setCurrentUserApplication] =
    useState<TeamApplication | null>(null);

  useEffect(() => {
    const supabase = supabaseClient();

    // Fetch current user's application
    if (profile?.email) {
      supabase
        .from('JOIN_US_TABLE')
        .select('*')
        .eq('email', profile.email)
        .single()
        .then(({ data }) => {
          if (data) setCurrentUserApplication(data);
        });
    }

    // Fetch all applications (for admins)
    if (role === 'Admin') {
      supabase
        .from('JOIN_US_TABLE')
        .select('*')
        .then(({ data }) => {
          if (data) setApplications(data);
        });

      supabase
        .from('waitlists')
        .select('*')
        .then(({ data }) => {
          if (data) setWaitlist(data);
        });
    }

    // Set up real-time subscriptions
    const applicationsChannel = supabase
      .channel('applications-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          if (
            payload.eventType === 'INSERT' ||
            payload.eventType === 'UPDATE'
          ) {
            const newData = payload.new as TeamApplication;
            if (newData.email === profile?.email) {
              setCurrentUserApplication(newData);
            }
          }

          if (role === 'Admin') {
            setApplications((current) => {
              switch (payload.eventType) {
                case 'INSERT':
                  return [...current, payload.new as TeamApplication];
                case 'UPDATE':
                  return current.map((app) =>
                    app.id === payload.new.id
                      ? (payload.new as TeamApplication)
                      : app
                  );
                case 'DELETE':
                  return current.filter((app) => app.id !== payload.old.id);
                default:
                  return current;
              }
            });
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(applicationsChannel);
    };
  }, [profile?.email, role]);

  // Calculate statistics from real data
  const stats = {
    total: applications.length,
    thisMonth: applications.filter((app) => {
      const createdAt = new Date(app.created_at);
      const now = new Date();
      return (
        createdAt.getMonth() === now.getMonth() &&
        createdAt.getFullYear() === now.getFullYear()
      );
    }).length,
    pending: applications.filter((app) => app.status === 'received').length,
    approved: applications.filter((app) => app.status === 'accepted').length,
  };

  const recentApplications = applications
    .sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
    .slice(0, 5);

  return (
    <div className='p-6'>
      <div className='mb-6'>
        <h1 className='text-2xl font-bold text-foreground'>
          Welcome back, {profile?.full_name?.split(' ')[0] || 'Team Member'}!
        </h1>
        <p className='text-muted-foreground'>
          Here's what's happening with your team today
        </p>
      </div>

      {/* Role-based content */}
      {role === 'Admin' ? (
        <AdminDashboard
          stats={stats}
          recentApplications={recentApplications}
          waitlistCount={waitlist.length}
        />
      ) : (
        <UserDashboard
          currentUserApplication={currentUserApplication}
          profile={profile}
        />
      )}
    </div>
  );
}

interface StatsCardProps {
  title: string;
  value: number;
  trend?: string;
  urgent?: boolean;
}

function StatsCard({ title, value, trend, urgent }: StatsCardProps) {
  return (
    <div
      className={`bg-card border rounded-lg p-6 ${urgent ? 'border-orange-200 bg-orange-50/50' : 'border-border'}`}
    >
      <div className='flex items-center justify-between mb-4'>
        <div
          className={`p-2 rounded-lg ${urgent ? 'bg-orange-100 text-orange-600' : 'bg-primary/10 text-primary'}`}
        >
          📊
        </div>
        {urgent && (
          <span className='text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full'>
            Needs Attention
          </span>
        )}
      </div>

      <div>
        <h3 className='text-sm font-medium text-muted-foreground'>{title}</h3>
        <p className='text-2xl font-bold text-foreground mt-1'>
          {value.toLocaleString()}
        </p>
        {trend && <p className='text-xs text-muted-foreground mt-1'>{trend}</p>}
      </div>
    </div>
  );
}

interface QuickActionButtonProps {
  title: string;
  description: string;
  href: string;
}

function QuickActionButton({
  title,
  description,
  href,
}: QuickActionButtonProps) {
  return (
    <a
      href={href}
      className='block p-4 bg-muted/50 rounded-lg hover:bg-muted transition-colors text-left'
    >
      <h4 className='font-medium text-sm'>{title}</h4>
      <p className='text-xs text-muted-foreground'>{description}</p>
    </a>
  );
}

// Admin Dashboard Component
interface AdminDashboardProps {
  stats: {
    total: number;
    thisMonth: number;
    pending: number;
    approved: number;
  };
  recentApplications: TeamApplication[];
  waitlistCount: number;
}

function AdminDashboard({
  stats,
  recentApplications,
  waitlistCount,
}: AdminDashboardProps) {
  return (
    <>
      {/* Statistics Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
        <StatsCard
          title='Total Applications'
          value={stats.total}
          trend={`${waitlistCount} in waitlist`}
        />
        <StatsCard title='This Month' value={stats.thisMonth} />
        <StatsCard
          title='Pending Review'
          value={stats.pending}
          urgent={stats.pending > 0}
        />
        <StatsCard title='Approved' value={stats.approved} />
      </div>

      {/* Recent Activity */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        <div className='bg-card border border-border rounded-lg p-6'>
          <h3 className='text-lg font-semibold mb-4'>Recent Applications</h3>
          <div className='space-y-3'>
            {recentApplications.length > 0 ? (
              recentApplications.map((app) => (
                <div
                  key={app.id}
                  className='flex items-center justify-between p-3 bg-muted/50 rounded-md'
                >
                  <div>
                    <p className='font-medium'>{app.full_name}</p>
                    <p className='text-sm text-muted-foreground'>
                      {app.role} • {app.identifier}
                    </p>
                  </div>
                  <StatusBadge status={app.status} />
                </div>
              ))
            ) : (
              <p className='text-muted-foreground text-center py-4'>
                No recent applications
              </p>
            )}
          </div>
        </div>

        <div className='bg-card border border-border rounded-lg p-6'>
          <h3 className='text-lg font-semibold mb-4'>Quick Actions</h3>
          <div className='grid grid-cols-2 gap-3'>
            <QuickActionButton
              title='Review Applications'
              description={`${stats.pending} pending`}
              href='/applications'
            />
            <QuickActionButton
              title='Team Members'
              description={`${stats.approved} active`}
              href='/members'
            />
            <QuickActionButton
              title='Waitlist'
              description={`${waitlistCount} entries`}
              href='/waitlist'
            />
            <QuickActionButton
              title='Settings'
              description='Manage team'
              href='/settings'
            />
          </div>
        </div>
      </div>
    </>
  );
}

// User Dashboard Component
interface UserDashboardProps {
  currentUserApplication: TeamApplication | null;
  profile: any;
}

function UserDashboard({
  currentUserApplication,
  profile,
}: UserDashboardProps) {
  if (!currentUserApplication) {
    return (
      <div className='bg-card border border-border rounded-lg p-6'>
        <h3 className='text-lg font-semibold mb-4'>Application Status</h3>
        <p className='text-muted-foreground'>
          No application found. Please contact support if you believe this is an
          error.
        </p>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Application Status */}
      <div className='bg-card border border-border rounded-lg p-6'>
        <h3 className='text-lg font-semibold mb-4'>Your Application Status</h3>
        <div className='flex items-center justify-between mb-4'>
          <div>
            <p className='font-medium'>{currentUserApplication.identifier}</p>
            <p className='text-sm text-muted-foreground'>
              Applied for {currentUserApplication.role}
            </p>
          </div>
          <StatusBadge status={currentUserApplication.status} />
        </div>

        <div className='grid grid-cols-2 gap-4 mt-4'>
          <div>
            <p className='text-sm text-muted-foreground'>Applied</p>
            <p className='font-medium'>
              {new Date(currentUserApplication.created_at).toLocaleDateString()}
            </p>
          </div>
          <div>
            <p className='text-sm text-muted-foreground'>Last Updated</p>
            <p className='font-medium'>
              {new Date(currentUserApplication.updated_at).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* NDA Status */}
      {currentUserApplication.status === 'accepted' && (
        <div className='bg-card border border-border rounded-lg p-6'>
          <h3 className='text-lg font-semibold mb-4'>Next Steps</h3>
          <NDAStatusComponent
            isNdaSigned={currentUserApplication.is_nda_signed}
          />
        </div>
      )}
    </div>
  );
}

// Helper Components
function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    received: { label: 'Pending', className: 'bg-yellow-100 text-yellow-800' },
    reviewed: { label: 'Under Review', className: 'bg-blue-100 text-blue-800' },
    accepted: { label: 'Approved', className: 'bg-green-100 text-green-800' },
    rejected: { label: 'Rejected', className: 'bg-red-100 text-red-800' },
  };

  const config =
    statusConfig[status as keyof typeof statusConfig] || statusConfig.received;

  return (
    <span className={`text-xs px-2 py-1 rounded-full ${config.className}`}>
      {config.label}
    </span>
  );
}

function NDAStatusComponent({ isNdaSigned }: { isNdaSigned: boolean | null }) {
  if (isNdaSigned === null) {
    return (
      <div className='flex flex-col space-y-3'>
        <p>NDA Needs To Be Signed</p>
        <a
          href='/dashboard/nda'
          className='bg-primary text-primary-foreground px-4 py-2 rounded-md w-fit hover:bg-primary/90 transition-colors'
        >
          View NDA
        </a>
      </div>
    );
  }

  return (
    <div>
      {isNdaSigned ? (
        <p>NDA Accepted - Welcome to the team dashboard!</p>
      ) : (
        <p>NDA Not Accepted - Please contact support.</p>
      )}
    </div>
  );
}
