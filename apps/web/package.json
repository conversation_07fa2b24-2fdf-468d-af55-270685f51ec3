{"name": "web", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3000 --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@thf/ui": "*", "@thf/providers": "*", "@thf/emails": "*", "@thf/supabase": "*", "@thf/store": "*", "@thf/constants": "*", "@thf/sanity": "*", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.0", "@thf/eslint-config": "*", "@thf/tailwind-config": "*", "@thf/typescript-config": "*", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^22.15.3", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "autoprefixer": "^10.4.20", "eslint": "^9.28.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "5.8.2"}}