'use client';

import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@thf/ui/components/form';
import { useState } from 'react';

export const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
});

export default function BriefForm() {
  const [submit, setSubmit] = useState('Subscribe');
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  // 2. Define a submit handler.
  async function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    setSubmit('Subscribing...');
    try {
      await fetch('/api/brief', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', // Fix typo: "Aplication/json" to "application/json"
        },
        body: JSON.stringify(values),
      });
    } catch (error) {
      console.log(error);
      setSubmit('⛔ Try Again...');
    }
    setSubmit('Subscribed...');
    console.log(values);
  }
  return (
    <div className='flex flex-col w-full text-center max-w-6xl space-y-6 pb-10 z-40 mx-auto'>
      <div>
        <p className='text-xl md:text-6xl font-bold text-white'>Brief Form</p>
      </div>
      <div className='flex flex-col w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className=' flex w-full'>
            {/* name and email */}
            <div className='bg-white flex w-full justify-between p-2 rounded-2xl'>
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormControl>
                      <input
                        placeholder='Enter your email'
                        type='email'
                        className='pl-2 h-full text-xl bg-white rounded-none outline-hidden w-full placeholder:text-sm md:placeholder:text-xl'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='text-xs' />
                  </FormItem>
                )}
              />
              {/* submitbtn */}
              <button
                className='rounded-xl bg-black text-lg inline-flex px-5 py-3 text-white'
                type='submit'
              >
                <span>{submit}</span>
              </button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
