'use client';

import { cn } from '@thf/ui/utils/cn';
import { motion } from 'framer-motion';
import { useState } from 'react';
import * as z from 'zod';
import AffForm from './aff';
import CollForm from './coll';
import VolForm from './vol';

export const collaboratorformSchema = z.object({
  first_name: z.string().min(2, {
    message: 'First Name must be at least 2 characters.',
  }),
  last_name: z.string().min(2, {
    message: 'Last Name must be at least 2 characters.',
  }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  number: z
    .string()
    .min(10, { message: 'Must be a valid mobile number' })
    .max(14, { message: 'Must be a valid mobile number' }),
  location: z.string().min(8, {
    message: 'Location must be at least 8 characters.',
  }),
  resume: z.instanceof(File).optional(),
  portfolio: z.string().url({
    message: 'Must be a link like https://www.thehuefactory.co/.',
  }),
  position: z.string(),
  position_other: z.string().optional(),
  referer: z.string().optional(),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
});
export const affiliateformSchema = z.object({
  first_name: z.string().min(2, {
    message: 'First Name must be at least 2 characters.',
  }),
  last_name: z.string().min(2, {
    message: 'Last Name must be at least 2 characters.',
  }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  number: z
    .string()
    .min(10, { message: 'Must be a valid mobile number' })
    .max(14, { message: 'Must be a valid mobile number' }),
  location: z.string().min(8, {
    message: 'Location must be at least 8 characters.',
  }),
  portfolio: z.string().url({
    message: 'Must be a link like https://www.thehuefactory.co/.',
  }),
  referer: z.string().optional(),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
});

export const volunteerformSchema = z.object({
  first_name: z.string().min(2, {
    message: 'First Name must be at least 2 characters.',
  }),
  last_name: z.string().min(2, {
    message: 'Last Name must be at least 2 characters.',
  }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  number: z
    .string()
    .min(10, { message: 'Must be a valid mobile number' })
    .max(14, { message: 'Must be a valid mobile number' }),
  location: z.string().min(8, {
    message: 'Location must be at least 8 characters.',
  }),
  resume: z.instanceof(File).optional(),
  portfolio: z.string().url({
    message: 'Must be a link like https://www.thehuefactory.co/.',
  }),
  referer: z.string().optional(),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
});

export default function JoinUsForm() {
  const [joinRole, setJoinRole] = useState<'col' | 'aff' | 'vol'>('col');

  return (
    <div className='w-full flex flex-col'>
      <div className='p-4 flex flex-col space-y-2'>
        <p className='text-xl font-semibold text-white'>
          Submit your application here
        </p>

        <div className='flex flex-row md:flex-row  space-x-4 items-center w-fit justify-start text-lg md:*:text-2xl'>
          <button
            onClick={() => setJoinRole('col')}
            className={cn(
              'p-2 flex relative items-center justify-center w-full text-white bg-transparent transition-all duration-200',
              joinRole === 'col' && 'text-accent-100 font-bold'
            )}
          >
            <span className=''>Collaborator</span>
            {joinRole === 'col' && (
              <motion.div
                layoutId='join_nav'
                className='w-1/3 h-1 rounded-full absolute bottom-1 bg-accent-100'
              />
            )}
          </button>
          <button
            onClick={() => setJoinRole('aff')}
            className={cn(
              'p-2 flex items-center justify-center w-full text-white bg-transparent relative transition-all duration-200',
              joinRole === 'aff' && 'text-accent-100 font-bold'
            )}
          >
            <span>Affiliate</span>
            {joinRole === 'aff' && (
              <motion.div
                layoutId='join_nav'
                className='w-1/3 h-1 rounded-full absolute bottom-1 bg-accent-100'
              />
            )}
          </button>
          <button
            onClick={() => setJoinRole('vol')}
            className={cn(
              'p-2 flex items-center justify-center w-full text-white bg-transparent relative transition-all duration-200',
              joinRole === 'vol' && 'text-accent-100 font-bold'
            )}
          >
            <span>Volunteer</span>
            {joinRole === 'vol' && (
              <motion.div
                layoutId='join_nav'
                className='w-1/3 h-1 rounded-full absolute bottom-1 bg-accent-100'
              />
            )}
          </button>
        </div>
      </div>
      {joinRole === 'col' ? (
        <CollForm />
      ) : joinRole === 'aff' ? (
        <AffForm />
      ) : joinRole === 'vol' ? (
        <VolForm />
      ) : null}
    </div>
  );
}
