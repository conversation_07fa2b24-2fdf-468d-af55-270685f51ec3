'use client';

import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@thf/ui/components/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@thf/ui/components/form';
import { Input } from '@thf/ui/components/input';
import { Textarea } from '@thf/ui/components/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@thf/ui/components/select';
import { useEffect, useState } from 'react';
import { ArrowRight, Check, Loader2, RotateCw, X } from 'lucide-react';
import Link from 'next/link';
import { supabaseClient } from '@thf/supabase/auth/client';

export const collaboratorformSchema = z.object({
  first_name: z.string().min(2, {
    message: 'First Name must be at least 2 characters.',
  }),
  last_name: z.string().min(2, {
    message: 'Last Name must be at least 2 characters.',
  }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  number: z
    .string()
    .min(10, { message: 'Must be a valid mobile number' })
    .max(14, { message: 'Must be a valid mobile number' }),
  location: z.string().min(8, {
    message: 'Location must be at least 8 characters.',
  }),
  resume: z.instanceof(File).optional(),
  portfolio: z.string().url({
    message: 'Must be a link like https://www.thehuefactory.co/.',
  }),
  position: z.string(),
  position_other: z.string().optional(),
  referer: z.string().optional(),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
});

const positionValues = [
  'Graphic Designer',
  'Photographer',
  'Videographer',
  'Developer',
  '3D/2D Artist',
  'Writer',
  'Other',
];

export default function CollForm() {
  const [submit, setSubmit] = useState('Submit');
  const supabase = supabaseClient();

  // collaborator
  const collaboratorform = useForm<z.infer<typeof collaboratorformSchema>>({
    resolver: zodResolver(collaboratorformSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      number: '',
      message: '',
      portfolio: '',
      location: '',
      referer: '',
      position: '',
      position_other: '',
    },
  });

  const po_value = collaboratorform.watch().position;

  async function onCollaboratorSubmit(
    values: z.infer<typeof collaboratorformSchema>
  ) {
    setSubmit('sending...');

    let resumeUrl = '';

    if (values.resume) {
      const { data, error } = await supabase.storage
        .from('resumes')
        .upload(`${values.email}-resume`, values.resume);

      if (error) {
        console.error('Error uploading file:', error);
        setSubmit('⛔ Try Again...');
        return;
      }

      resumeUrl = supabase.storage.from('resumes').getPublicUrl(data.path)
        .data.publicUrl;
    }

    const { error, status } = await supabase.from('JOIN_US_TABLE').insert({
      full_name: `${values.first_name} ${values.last_name}`,
      email: values.email,
      phone: values.number,
      position: values.position,
      position_other: values.position_other,
      message: values.message,
      referer: values.referer,
      resume_url: resumeUrl,
      join_role: 'Collaborator',
      location: values.location,
    });

    if (!error) {
      try {
        await fetch('/api/join', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...values, resume_url: resumeUrl }),
        });
        setSubmit('🎉 Message Sent...');
      } catch (error) {
        console.log(error);
        setSubmit('⛔ Try Again...');
      }
    } else {
      setSubmit('⛔ Try Again...');
      console.log(error);
    }
  }

  return (
    <div className='w-full flex flex-col p-4'>
      <Form {...collaboratorform}>
        <form
          onSubmit={collaboratorform.handleSubmit(onCollaboratorSubmit)}
          className='space-y-8'
        >
          {/* name and email */}
          <div className='grid sm:grid-cols-2 gap-4'>
            <FormField
              control={collaboratorform.control}
              name='first_name'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Your First Name Here'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={collaboratorform.control}
              name='last_name'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Your Last Name Here'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={collaboratorform.control}
              name='email'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Email Address'
                      type='email'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={collaboratorform.control}
              name='number'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Phone Number'
                      type='tel'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          <div className='grid sm:grid-cols-2 gap-4'>
            <FormField
              control={collaboratorform.control}
              name='location'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Location (City)'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={collaboratorform.control}
              name='resume'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    {/* <Input
                      type='file'
                      accept='.pdf,.doc,.docx'
                      placeholder='Your Resume'
                      onChange={(e) => field.onChange(e.target.files?.[0])}
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/80 border-[#252525] text-neutral-400'
                    /> */}
                    <div className='relative'>
                      <Input
                        type='file'
                        accept='.pdf,.doc,.docx'
                        onChange={(e) => field.onChange(e.target.files?.[0])}
                        className='absolute inset-0 opacity-0 w-full h-full cursor-pointer'
                      />
                      <div className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/80 border-[#252525] text-neutral-400 py-1.5 px-4 flex items-center justify-between'>
                        <span>
                          {field.value ? field.value.name : 'Your Resume'}
                        </span>
                        <span>Browse</span>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>

          {/*potions */}
          <div className='grid sm:grid-cols-2 gap-4'>
            <FormField
              control={collaboratorform.control}
              name='position'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl className='border rounded-lg h-auto w-full outline-hidden ring-offset-accent-100 bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'>
                        <SelectTrigger>
                          <SelectValue placeholder='Select Your Position' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className='border *:ring-offset-accent-100 rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c] border-[#252525] text-neutral-400'>
                        {positionValues.map((v, i) => (
                          <SelectItem key={i} value={v}>
                            {v}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            {po_value === 'Other' && (
              <FormField
                control={collaboratorform.control}
                name='position_other'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder='Enter Other Position'
                        type='tel'
                        className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='text-xs' />
                  </FormItem>
                )}
              />
            )}
          </div>
          {/* portfolio */}
          <FormField
            control={collaboratorform.control}
            name='portfolio'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder='Portfolio Link (Social Handles, Website Portfolio etc..)'
                    className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage className='text-xs' />
              </FormItem>
            )}
          />
          <FormField
            control={collaboratorform.control}
            name='referer'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder='How did you hear about this role?'
                    className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage className='text-xs' />
              </FormItem>
            )}
          />
          {/* textarea */}
          <div className='grid gap-4'>
            <FormField
              control={collaboratorform.control}
              name='message'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder='Tell us a few words ...about your self etc]'
                      className='border rounded-lg h-auto min-h-44  w-full outline-hidden ring-offset-accent-100 bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          {/* submitbtn */}
          <div className='flex items-center justify-between pt-10'>
            <div className='hidden md:flex'>
              <Button
                variant={'main'}
                size={'none'}
                type='submit'
                className='w-fit'
              >
                {submit}
              </Button>
            </div>
            <div className='md:hidden'>
              <Button
                variant={'main_sm'}
                size={'none'}
                type='submit'
                className='w-fit'
              >
                {submit}
              </Button>
            </div>
            <div className='space-y-1 flex flex-col'>
              <p className='text-sm/tight  md:text-2xl text-white'>
                Hate contact forms?
              </p>
              <Link
                href='mailto:<EMAIL>'
                className='text-sm/tight text-accent-100 hover:text-white'
              >
                <EMAIL>
              </Link>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
