'use client';

import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@thf/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@thf/ui/components/form';
import { Input } from '@thf/ui/components/input';
import { Textarea } from '@thf/ui/components/textarea';
import { useState } from 'react';
import Link from 'next/link';
import { supabaseClient } from '@thf/supabase/auth/client';

export const volunteerformSchema = z.object({
  first_name: z.string().min(2, {
    message: 'First Name must be at least 2 characters.',
  }),
  last_name: z.string().min(2, {
    message: 'Last Name must be at least 2 characters.',
  }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  number: z
    .string()
    .min(10, { message: 'Must be a valid mobile number' })
    .max(14, { message: 'Must be a valid mobile number' }),
  location: z.string().min(8, {
    message: 'Location must be at least 8 characters.',
  }),
  resume: z.instanceof(File).optional(),
  portfolio: z.string().url({
    message: 'Must be a link like https://www.thehuefactory.co/.',
  }),
  referer: z.string().optional(),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
});

export default function VolForm() {
  const [submit, setSubmit] = useState('Submit');
  const supabase = supabaseClient();

  const volunteerform = useForm<z.infer<typeof volunteerformSchema>>({
    resolver: zodResolver(volunteerformSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      number: '',
      message: '',
      portfolio: '',
      location: '',
      referer: '',
    },
  });

  async function onvolunteerSubmit(
    values: z.infer<typeof volunteerformSchema>
  ) {
    setSubmit('sending...');

    let resumeUrl = '';

    if (values.resume) {
      const { data, error } = await supabase.storage
        .from('resumes')
        .upload(`${values.email}-resume`, values.resume);

      if (error) {
        console.error('Error uploading file:', error);
        setSubmit('⛔ Try Again...');
        return;
      }

      resumeUrl = supabase.storage.from('resumes').getPublicUrl(data.path)
        .data.publicUrl;
    }

    const { error, status } = await supabase.from('JOIN_US_TABLE').insert({
      full_name: `${values.first_name} ${values.last_name}`,
      email: values.email,
      phone: values.number,
      position: null,
      position_other: null,
      message: values.message,
      referer: values.referer,
      resume_url: resumeUrl,
      join_role: 'Volunteer',
      location: values.location,
    });

    if (!error) {
      try {
        await fetch('/api/join-volunteer', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...values, resume_url: resumeUrl }),
        });
        setSubmit('🎉 Message Sent...');
      } catch (error) {
        console.log(error);
        setSubmit('⛔ Try Again...');
      }
    } else {
      setSubmit('⛔ Try Again...');
      console.log(error);
    }
  }

  return (
    <div className='w-full flex flex-col p-4'>
      <Form {...volunteerform}>
        <form
          onSubmit={volunteerform.handleSubmit(onvolunteerSubmit)}
          className='space-y-8'
        >
          {/* name and email */}
          <div className='grid sm:grid-cols-2 gap-4'>
            <FormField
              control={volunteerform.control}
              name='first_name'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Your First Name Here'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={volunteerform.control}
              name='last_name'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Your Last Name Here'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={volunteerform.control}
              name='email'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Email Address'
                      type='email'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={volunteerform.control}
              name='number'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Phone Number'
                      type='tel'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          <div className='grid sm:grid-cols-2 gap-4'>
            <FormField
              control={volunteerform.control}
              name='location'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Location (City)'
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={volunteerform.control}
              name='resume'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    {/* <Input
                      type='file'
                      accept='.pdf,.doc,.docx'
                      onChange={(e) => field.onChange(e.target.files?.[0])}
                      className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/80 border-[#252525] text-neutral-400'
                    /> */}
                    <div className='relative'>
                      <Input
                        type='file'
                        accept='.pdf,.doc,.docx'
                        onChange={(e) => field.onChange(e.target.files?.[0])}
                        className='absolute inset-0 opacity-0 w-full h-full cursor-pointer'
                      />
                      <div className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/80 border-[#252525] text-neutral-400 py-1.5 px-4 flex items-center justify-between'>
                        <span>
                          {field.value ? field.value.name : 'Your Resume'}
                        </span>
                        <span>Browse</span>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          {/* portfolio */}
          <FormField
            control={volunteerform.control}
            name='portfolio'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder='Portfolio Link (Social Handles, Website Portfolio etc..)'
                    className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage className='text-xs' />
              </FormItem>
            )}
          />
          <FormField
            control={volunteerform.control}
            name='referer'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder='How did you hear about this role?'
                    className='border rounded-lg h-auto w-full outline-hidden bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage className='text-xs' />
              </FormItem>
            )}
          />
          {/* textarea */}
          <div className='grid gap-4'>
            <FormField
              control={volunteerform.control}
              name='message'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder='Tell us a few words ...about your self etc]'
                      className='border rounded-lg h-auto min-h-44  w-full outline-hidden ring-offset-accent-100 bg-[#1c1c1c]/20 border-[#252525] text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          {/* submitbtn */}
          <div className='flex items-center justify-between pt-10'>
            <div className='hidden md:flex'>
              <Button
                variant={'main'}
                size={'none'}
                type='submit'
                className='w-fit'
              >
                {submit}
              </Button>
            </div>
            <div className='md:hidden'>
              <Button
                variant={'main_sm'}
                size={'none'}
                type='submit'
                className='w-fit'
              >
                {submit}
              </Button>
            </div>
            <div className='space-y-1 flex flex-col'>
              <p className='text-sm/tight text-white md:text-2xl'>
                Hate contact forms?
              </p>
              <Link
                href='mailto:<EMAIL>'
                className='text-sm/tight text-accent-100 hover:text-white'
              >
                <EMAIL>
              </Link>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
