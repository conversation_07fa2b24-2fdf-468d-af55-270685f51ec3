'use client';

import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@thf/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@thf/ui/components/form';
import { Input } from '@thf/ui/components/input';
import { useState } from 'react';
import { supabaseClient } from '@thf/supabase/auth/client';
import { TextEffect } from '@thf/ui/animations/text-effect';

export const formSchema = z.object({
  full_name: z.string().min(2, {
    message: 'Full Name must be at least 2 characters.',
  }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
});

export default function WaitlistForm() {
  const [submit, setSubmit] = useState('Join');
  const [CheckMail, setCheckMail] = useState(false);
  const supabase = supabaseClient();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      full_name: '',
      email: '',
    },  
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setSubmit('joining...');

    const { error } = await supabase.from('waitlists').insert({
      full_name: values.full_name,
      email_address: values.email,
    });

    if (!error) {
      try {
        await fetch('/api/waitlist-new', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...values }),
        });
        setSubmit('🎉 Joined...');
        setCheckMail(true);
      } catch (error) {
        console.log(error);
        setSubmit('⛔ Try Again...');
      }
    } else {
      setSubmit('⛔ Try Again...');
      if (
        error.message ===
        'duplicate key value violates unique constraint "waitlists_email_address_key"'
      ) {
        setSubmit('Already Joined...');
      }
      console.log(error);
    }
    form.reset();
  }

  return (
    <div className='w-full flex flex-col px-4'>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className='space-y-10 md:space-y-20 min-w-[240px] w-full flex flex-col'
        >
          {/* name and email */}
          <div className='grid md:grid-cols-3 w-full gap-2'>
            <FormField
              control={form.control}
              name='full_name'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormControl>
                    <Input
                      placeholder='First Name'
                      className='rounded-lg h-auto w-full text-lg outline-hidden bg-white text-neutral-400 placeholder:text-neutral-300'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='email'
              render={({ field }) => (
                <FormItem className='md:col-span-2 w-full'>
                  <FormControl>
                    <Input
                      placeholder='Email'
                      type='email'
                      className='rounded-lg h-auto w-full text-lg outline-hidden  bg-white text-neutral-400 placeholder:text-neutral-300'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          {/* submitbtn */}
          <div className='flex flex-col items-center justify-center w-full'>
            <Button
              variant={'mainsm'}
              size={'none'}
              type='submit'
              className='ring-offset-accent-100 hover:bg-white hover:text-accent-100 md:hidden '
            >
              <span>{submit}</span>
            </Button>
            <Button
              variant={'main'}
              size={'none'}
              type='submit'
              className='ring-offset-accent-100 hover:bg-white hover:text-accent-100 hidden md:flex '
            >
              <span>{submit}</span>
            </Button>
            {CheckMail && (
              <TextEffect
                per='char'
                preset='blur'
                className='text-white text-sm pt-2'
              >
                We&apos;ve sent you a mail!
              </TextEffect>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}
