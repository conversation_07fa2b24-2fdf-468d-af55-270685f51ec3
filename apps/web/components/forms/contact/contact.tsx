'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@thf/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@thf/ui/components/form';
import { Input } from '@thf/ui/components/input';
import { Textarea } from '@thf/ui/components/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@thf/ui/components/select';
import { useState } from 'react';
import Link from 'next/link';

export const contactFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().min(10, { message: 'Please enter a valid phone number.' }),
  reason: z.string().min(1, { message: 'Please select a reason for contact.' }),
  message: z
    .string()
    .min(10, { message: 'Please enter a message (at least 10 characters).' }),
});

export default function Contact() {
  const [submit, setSubmit] = useState('Send');
  const contactForm = useForm<z.infer<typeof contactFormSchema>>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      reason: '',
      message: '',
    },
  });

  async function onContactSubmit(values: z.infer<typeof contactFormSchema>) {
    setSubmit('sending...');
    try {
      await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', // Fix typo: "Aplication/json" to "application/json"
        },
        body: JSON.stringify(values),
      });
    } catch (error) {
      console.log(error);
      setSubmit('⛔ Try Again...');
    }
    setSubmit('🎉 Message Sent...');
  }

  return (
    <div className='container mx-auto px-1 py-6'>
      <section className='space-y-6'>
        <h2 className='text-2xl font-semibold'>Contact Us</h2>
        <Form {...contactForm}>
          <form
            onSubmit={contactForm.handleSubmit(onContactSubmit)}
            className='space-y-4'
          >
            <div className='grid md:grid-cols-2 gap-4'>
              <FormField
                control={contactForm.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Your name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={contactForm.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type='email' placeholder='Your email' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className='grid md:grid-cols-2 gap-4'>
              <FormField
                control={contactForm.control}
                name='phone'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input
                        type='tel'
                        placeholder='Your phone number'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={contactForm.control}
                name='reason'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select a Service' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='consulting'>Consulting</SelectItem>
                        <SelectItem value='design'>Design</SelectItem>
                        <SelectItem value='development'>Development</SelectItem>
                        <SelectItem value='marketing'>Marketing</SelectItem>
                        <SelectItem value='branding'>Experience</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={contactForm.control}
              name='message'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Details</FormLabel>
                  <FormControl>
                    <Textarea placeholder='Details' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className='flex items-center justify-between pt-10'>
              <div className='hidden md:flex'>
                <Button
                  variant={'main'}
                  size={'none'}
                  type='submit'
                  className='w-fit'
                >
                  {submit}
                </Button>
              </div>
              <div className='md:hidden'>
                <Button
                  variant={'main_sm'}
                  size={'none'}
                  type='submit'
                  className='w-fit'
                >
                  {submit}
                </Button>
              </div>
              <div className='space-y-1 flex flex-col'>
                <p className='text-sm/tight  md:text-2xl'>
                  Hate contact forms?
                </p>
                <Link
                  href='mailto:<EMAIL>'
                  className='text-sm/tight text-accent-100 hover:text-white'
                >
                  <EMAIL>
                </Link>
              </div>
            </div>
          </form>
        </Form>
      </section>
    </div>
  );
}
