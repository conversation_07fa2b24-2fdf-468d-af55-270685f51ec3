'use client';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@thf/ui/components/form';
import { supabaseClient } from '@thf/supabase/auth/client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

export const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
});

export default function NotificationForm() {
  const [submit, setSubmit] = useState('Subscribe');
  const supabase = supabaseClient();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setSubmit('Subscribing...');
    // await fetch('/api/quote', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json', // Fix typo: "Aplication/json" to "application/json"
    //   },
    //   body: JSON.stringify(values),
    // });
    // setSubmit('Done...');
    const { error } = await supabase.from('Emails').insert({
      email_address: values.email,
    });
    if (!error) {
      try {
        await fetch('/api/notifications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });
      } catch (error) {
        console.log(error);
        setSubmit('⛔ Try Again...');
      }
      setSubmit('Subscribed...');
      form.reset();
    } else if (
      error.message ==
      `duplicate key value violates unique constraint \"Emails_email_address_key\"`
    ) {
      setSubmit('Already Joined...');
    } else {
      setSubmit('⛔ Try Again...');
      console.log(error);
    }
  }
  return (
    <div className='flex flex-col w-full text-center max-w-6xl space-y-6 pb-10 z-40 mx-auto'>
      <div>
        <p className='text-xl md:text-6xl font-bold text-white'>
          Subscribe to our newsletter!{' '}
        </p>
      </div>
      <div className='flex flex-col w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className=' flex w-full'>
            {/* name and email */}
            <div className='bg-white grid md:grid-cols-8 w-full gap-2 p-2 rounded-2xl'>
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem className='w-full md:col-span-6'>
                    <FormControl>
                      <input
                        placeholder='Enter your email'
                        type='email'
                        className='p-4 h-full text-lg md:text-xl bg-white rounded-lg outline-hidden w-full placeholder:text-sm md:placeholder:text-xl'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='text-xs' />
                  </FormItem>
                )}
              />
              {/* submitbtn */}
              <button
                className='rounded-xl md:col-span-2 text-center w-full justify-center bg-black text-lg inline-flex px-5 py-3 text-white'
                type='submit'
              >
                <span>{submit}</span>
              </button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
