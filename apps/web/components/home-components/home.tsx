'use client';
import mobile_sign from '../../public/assets/imgs/icons/sign.svg';
import Image from 'next/image';

import hughie from '../../public/assets/imgs/logos/hughie_01.svg';
import { TheCreativePowerHouseSvg } from '@thf/ui/svgs/imgs/the-creative-powerhouse';
import { cn } from '@thf/ui/utils/cn';
import { motion, useInView, useScroll } from 'framer-motion';
import { useRef } from 'react';
import { BlurIn } from '@thf/ui/animations/blur-in';
import { brandproject } from '../../../../types/types';
import { content_brand_sign, content_text_lgog } from '../../lib/imgs';
import { SERVICES_ARRAY } from '../../lib/services-array';
import Card from '../company-components/services-card';
import NotificationForm from '../forms/notifications';
import { HomeCarosel, HomeCaroselMobile } from './home-carousel';

type props = {
  projects: brandproject[];
};

export function HomePage({ projects }: props) {
  const container = useRef(null);
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: true, amount: 0.8 });

  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start start', 'end end'],
  });
  return (
    <main className='z-0'>
      {/* <HomePageParallax /> */}
      <div className='flex-col flex fixed justify-between inset-0 bg-accent-100 -z-10 -mb-10'>
        <BlurIn className=' w-full mx-auto p mt-16 flex-1 justify-center items-center flex flex-col'>
          <Image alt='' src={content_text_lgog} className=' w-full' />
        </BlurIn>
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: -40 }}
          transition={{
            type: 'spring',
            stiffness: 260,
            damping: 20,
            delay: 2,
            duration: 3000,
          }}
          className='px-2 md:px-40 flex flex-col items-center w-full'
        >
          <Image alt='' src={hughie} className='md:max-w-[72%] w-full' />
        </motion.div>
      </div>
      {/* section */}
      <section className='relative z-50 mt-[92dvh] overflow-hidden drop-shadow-2xl'>
        <div className='-mb-20 max-h-[60rem] rounded-t-3xl overflow-hidden bg-white'>
          <div className=''>
            <motion.div
              initial={{ opacity: 0, y: 80 }}
              whileInView={{ opacity: 1, y: -80 }}
              viewport={{ once: true, amount: 0.5 }}
              transition={{
                type: 'spring',
                stiffness: 260,
                damping: 20,
                delay: 0.5,
                duration: 1000,
              }}
              className='flex flex-col w-full items-center justify-center pt-10'
            >
              <Image
                alt=''
                src={mobile_sign}
                className='flex md:hidden w-[80dvw] max-h-[50rem] object-cover object-center -mb-72 mt-20 -rotate-3 h-full pl-6'
              />
            </motion.div>
            <motion.div
              ref={containerRef}
              initial={{ opacity: 0, y: 80 }}
              animate={
                isInView ? { opacity: 1, y: -80 } : { opacity: 0, y: 80 }
              }
              transition={{
                type: 'spring',
                stiffness: 260,
                damping: 20,
                delay: 0.5,
                duration: 1000,
              }}
            >
              <Image
                alt=''
                src={content_brand_sign}
                className='hidden md:flex w-full max-h-[60rem] md:object-cover -mt-20 -rotate-3 h-full'
              />
            </motion.div>
          </div>
        </div>
        <HomeCarosel projects={projects} />
        <HomeCaroselMobile projects={projects} />
      </section>
      {/* rest of section */}
      <div
        className={cn(
          'flex flex-col w-full bg-accent-100 px-20 md:-mt-20 md:py-28 z-10'
        )}
      >
        <div className='flex flex-col items-center justify-center space-y-4 md:pt-14'>
          <TheCreativePowerHouseSvg className=' max-w-[70dvw] h-40 md:h-auto w-auto ' />
        </div>
        <p className='text-white text-center text-sm/tight md:text-lg md:pt-12 pb-10 md:pb-0'>
          We&apos;re a full service creative company dedicated to helping brands
          thrive!
        </p>
        <div ref={container} className='flex flex-col relative md:mt-[10dvh]'>
          {SERVICES_ARRAY.map((s, i) => {
            const targetScale = 1 - (SERVICES_ARRAY.length - i) * 0.05;
            return (
              <Card
                id={s.id}
                icon={s.icon}
                title={s.title}
                description={s.description}
                features={s.features}
                key={`p_${i}`}
                {...SERVICES_ARRAY}
                progress={scrollYProgress}
                range={[i * 0.25, 1]}
                targetScale={targetScale}
              />
            );
          })}
        </div>
      </div>
      <div
        id='Notifications'
        className='bg-accent-100 p-4 flex flex-col w-full pt-20'
      >
        <NotificationForm />
        {/* <BriefForm /> */}
      </div>
    </main>
  );
}
