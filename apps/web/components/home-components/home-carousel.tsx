'use client';
import { motion, useAnimation } from 'framer-motion';
import Autoplay from 'embla-carousel-autoplay';

import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@thf/ui/components/carousel';
import { brandproject } from '../../../../types/types';

import MediaComponent from '../studio/media-component'; 
import { useCallback, useEffect, useRef, useState } from 'react';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

type props = {
  projects: brandproject[];
};

export function HomeCarosel({ projects }: props) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [progress, setProgress] = useState(0);

  const progressRef = useRef<NodeJS.Timeout | null>(null);

  const AUTOPLAY_DELAY = 5000;

  const autoplay = useCallback(
    () =>
      Autoplay({
        delay: AUTOPLAY_DELAY,
        stopOnInteraction: false,
        rootNode: (emblaRoot) => emblaRoot.parentElement,
      }),
    []
  );

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    const onSelect = () => {
      setCurrent(api.selectedScrollSnap() + 1);
      setProgress(0);
      if (progressRef.current) clearInterval(progressRef.current);
      progressRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 0.01;
          return newProgress >= 1 ? 1 : newProgress;
        });
      }, AUTOPLAY_DELAY / 100);
    };

    api.on('select', onSelect);
    onSelect(); // Initialize progress for the first slide

    return () => {
      api.off('select', onSelect);
      if (progressRef.current) clearInterval(progressRef.current);
    };
  }, [api]);

  return (
    <div className='rounded-b-3xl bg-white z-40 relative pb-5 px-4 hidden md:flex flex-col'>
      <Carousel
        plugins={[autoplay()]}
        setApi={setApi}
        opts={{
          loop: true,
        }}
        className='relative group'
      >
        <ProgressBar current={current} count={count} progress={progress} />
        <CarouselContent>
          {projects.map((p) => {
            return (
              <CarouselItem
                key={p._id}
                className='md:h-[90vh] rounded-3xl overflow-hidden z-20 relative flex'
              >
                <MediaComponent
                  controls={true}
                  media={p.carouselLandscape}
                  className='rounded-3xl object-cover md:h-[90vh] md:object-left-top'
                />
                <Link
                  href={`/projects/${p.slug}`}
                  className='absolute bottom-4 left-1/2 -translate-x-1/2 *:text-white flex flex-col items-center justify-center text-center'
                >
                  <div className='font-bold flex items-center w-[300px] whitespace-nowrap justify-center'>
                    <p className='flex items-center space-x-2'>
                      <span>{p.title}</span>
                      <span className='w-0 group-hover:w-6 overflow-hidden transition-all ease-linear duration-200'>
                        <ArrowRight className='size-6' />
                      </span>
                    </p>
                  </div>
                  <p className='font-bold text-2xl'>{p.subtitle}</p>
                </Link>
              </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>
    </div>
  );
}
export function HomeCaroselMobile({ projects }: props) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [progress, setProgress] = useState(0);

  const progressRef = useRef<NodeJS.Timeout | null>(null);

  const AUTOPLAY_DELAY = 5000;

  const autoplay = useCallback(
    () =>
      Autoplay({
        delay: AUTOPLAY_DELAY,
        stopOnInteraction: false,
        rootNode: (emblaRoot) => emblaRoot.parentElement,
      }),
    []
  );

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    const onSelect = () => {
      setCurrent(api.selectedScrollSnap() + 1);
      setProgress(0);
      if (progressRef.current) clearInterval(progressRef.current);
      progressRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 0.01;
          return newProgress >= 1 ? 1 : newProgress;
        });
      }, AUTOPLAY_DELAY / 100);
    };

    api.on('select', onSelect);
    onSelect(); // Initialize progress for the first slide

    return () => {
      api.off('select', onSelect);
      if (progressRef.current) clearInterval(progressRef.current);
    };
  }, [api]);

  // useEffect(() => {
  //   if (!api) {
  //     return;
  //   }

  //   setCount(api.scrollSnapList().length);
  //   setCurrent(api.selectedScrollSnap() + 1);

  //   api.on('select', () => {
  //     setCurrent(api.selectedScrollSnap() + 1);
  //   });
  // }, [api]);

  return (
    <div className='rounded-b-3xl bg-white z-40 relative pb-5 px-4 md:hidden flex flex-col'>
      <Carousel
        plugins={[autoplay()]}
        setApi={setApi}
        opts={{
          loop: true,
        }}
        className='relative group'
      >
        <ProgressBar current={current} count={count} progress={progress} />
        <CarouselContent>
          {projects.map((p) => {
            return (
              <CarouselItem
                key={p._id}
                className='md:h-[90vh] rounded-3xl overflow-hidden z-20 relative flex'
              >
                <MediaComponent
                  controls={true}
                  media={p.carouselPortrait}
                  className='rounded-3xl object-cover md:h-[90vh] md:object-left-top'
                />
                <Link
                  href={`/projects/${p.slug}`}
                  className='absolute bottom-4 left-1/2 -translate-x-1/2 *:text-white flex flex-col items-center justify-center text-center'
                >
                  <div className='font-bold flex items-center w-[300px] whitespace-nowrap justify-center'>
                    <p className='flex items-center space-x-2'>
                      <span>{p.title}</span>
                      <span className='w-0 group-hover:w-6 overflow-hidden transition-all ease-linear duration-200'>
                        <ArrowRight className='size-6' />
                      </span>
                    </p>
                  </div>
                  <p className='font-bold text-2xl'>{p.subtitle}</p>
                </Link>
              </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>
    </div>
  );
}

const ProgressBar = ({
  current,
  count,
  progress,
}: {
  current: number;
  count: number;
  progress: number;
}) => {
  return (
    <div className='flex w-full gap-2 mb-4 absolute top-3 z-50 px-4'>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          className='h-1 bg-[rgb(255,255,255,0.2)] rounded-full overflow-hidden'
          style={{ flex: 1 }}
        >
          <motion.div
            className='h-full bg-white'
            initial={{ width: 0 }}
            layoutId='progressBarr'
            animate={{
              width: `${index + 1 === current ? progress * 100 : 0}%`,
            }}
            transition={{ duration: 0.1 }}
          />
        </motion.div>
      ))}
    </div>
  );
};
