'use client';
import { Button } from '@thf/ui/components/button';
import { cn } from '@thf/ui/utils/cn';
import { CldImage } from 'next-cloudinary';
import { useEffect, useRef, useState } from 'react';
import { img, vid } from '../../../../types/types';

type videoProps = {
  vid: vid;
  className?: string;
  controls?: boolean;
  autoplay?: boolean;
  alowHover?: boolean;
};

type imgFormart = 'webp';
type vidFormart = 'mp4';

type videoControlsProps = {
  progress: number;
  isPaused: boolean;
  onPlayPause: () => void;
  size?: number;
  width?: number;
};

export default function MediaComponent({
  media,
  autoplay,
  className,
  controls,
  alowHover,
}: {
  media: img | vid;
  className?: string;
  controls?: boolean;
  autoplay?: boolean;
  alowHover?: boolean;
}) {
  if (media.format === 'mp4') {
    return (
      <Vid
        autoplay={autoplay}
        className={className}
        controls={controls}
        vid={media as vid}
        alowHover={alowHover}
      />
    );
  } else {
    return <Img img={media as img} className={className} />;
  }
}

export function Img({ img, className }: { img: img; className?: string }) {
  return (
    <div className={cn(className, 'w-full h-full')}>
      <CldImage
        width={img.width}
        height={img.height}
        src={img.public_id}
        sizes='100vw'
        priority
        alt={img.display_name + '_thehuefactory'}
        className={cn(className, 'w-full h-full')}
      />
    </div>
  );
}

export function Vid({
  vid,
  className,
  controls,
  autoplay,
  alowHover,
}: videoProps) {
  const videoRef = useRef<HTMLVideoElement>(null);

  const [isPaused, setIsPaused] = useState(false);
  const [isHover, setIsHover] = useState(false);
  const [videoDuration, setVideoDuration] = useState<number>();
  const [videoProgress, setVideoProgress] = useState<number>(0);

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      setVideoDuration(video.duration);
    }
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const updateProgress = () => {
      const currentTime = video.currentTime;
      const duration = video.duration;
      if (duration > 0) {
        setVideoProgress(currentTime / duration);
      }
    };

    if (!isPaused) {
      video.addEventListener('timeupdate', updateProgress);
    }

    return () => {
      video.removeEventListener('timeupdate', updateProgress);
    };
  }, [isPaused]);

  const togglePlayPause = () => {
    const video = videoRef.current;
    if (video) {
      setIsPaused(!video.paused);
      video.paused ? video.play() : video.pause();
    }
  };

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.load();
    }
  }, [vid.secure_url]);

  return (
    <div
      className={cn(
        className,
        'relative flex w-full items-center justify-center overflow-hidden'
      )}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      {!controls ? (
        !alowHover ? null : (
          alowHover &&
          isHover && (
            <VideoControls
              progress={videoProgress}
              isPaused={isPaused}
              onPlayPause={togglePlayPause}
              size={48}
            />
          )
        )
      ) : (
        <VideoControls
          progress={videoProgress}
          isPaused={isPaused}
          onPlayPause={togglePlayPause}
          size={48}
        />
      )}
      <video
        ref={videoRef}
        autoPlay={autoplay}
        muted
        width={vid.width}
        height={vid.height}
        playsInline
        loop
        preload='metadata'
        aria-label='video player'
      >
        <source src={vid.secure_url} type='video/mp4' />
        Your browser doesnot support video tag
      </video>
    </div>
  );
}

function VideoControls({
  isPaused,
  onPlayPause,
  progress,
  size = 59,
  width = 3,
}: videoControlsProps) {
  const center = size / 2;
  const radius = center - width;
  const dashArray = 2 * Math.PI * radius;
  const dashOffset = dashArray * (1 - progress);
  return (
    <div className='absolute inset-0 flex flex-col items-center justify-center z-10'>
      <div className='relative flex items-center justify-center'>
        <svg width={size} height={size} style={{ transform: 'rotate(-90deg)' }}>
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill='transparent'
            stroke='#a3a3a3'
            strokeWidth={width}
          />
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill='transparent'
            stroke='#ffffff'
            strokeWidth={width}
            strokeDasharray={dashArray}
            strokeDashoffset={dashOffset}
            strokeLinecap='round'
          />
        </svg>
        <div className='absolute'>
          <Button
            variant={'default'}
            size={'icon'}
            className='group flex size-8 cursor-pointer items-center justify-center'
            onClick={onPlayPause}
          >
            {isPaused ? (
              <svg
                xmlns='http://www.w3.org/2000/svg'
                viewBox='0 0 24 24'
                fill='currentColor'
                className='size-5'
              >
                <path
                  fill-rule='evenodd'
                  d='M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z'
                  clip-rule='evenodd'
                />
              </svg>
            ) : (
              <svg
                xmlns='http://www.w3.org/2000/svg'
                viewBox='0 0 24 24'
                fill='currentColor'
                className='size-6'
              >
                <path
                  fill-rule='evenodd'
                  d='M6.75 5.25a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H7.5a.75.75 0 0 1-.75-.75V5.25Zm7.5 0A.75.75 0 0 1 15 4.5h1.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H15a.75.75 0 0 1-.75-.75V5.25Z'
                  clip-rule='evenodd'
                />
              </svg>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
