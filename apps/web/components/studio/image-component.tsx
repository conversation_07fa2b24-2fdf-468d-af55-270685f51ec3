'use client';

import { CldImage } from 'next-cloudinary';
import { img } from '../../../../types/types';

export function Img({ img }: { img: img }) {
  return (
    <div className='w-full overflow-hidden rounded-xl'>
      <CldImage
        width={img.width}
        height={img.height}
        src={img.public_id}
        sizes='100vw'
        alt={img.display_name + '_innolensgh'}
      />
    </div>
  );
}

// export function StudioImg({img}:{img:aboutType['team_images'][0]}) {
//   const {dimensions,alt} = img
//   return (
//     <Suspense>
//       <Image
//         height={dimensions ? dimensions. : 960}
//         width={dimensions ? dimensions.width : 600}
//         sizes="600px"
//         className="pointer-events-none h-96 object-cover"
//         placeholder="blur-sm"
//         loading="lazy"
//         blurDataURL={urlFor(img)
//           .height(dimensions ? dimensions.height : 960)
//           .width(dimensions ? dimensions.width : 600)
//           .blur(89)
//           .url()}
//         alt={alt ? alt : "img"}
//         src={urlFor(img)
//           .height(dimensions ? dimensions.height : 960)
//           .width(dimensions ? dimensions.width : 600)
//           .url()}
//       />
//     </Suspense>
//   );
// }
