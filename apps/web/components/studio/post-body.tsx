'use cleint';
import { PortableText, PortableTextComponents } from '@portabletext/react';
import { urlForImage } from '@thf/sanity/lib';
import { cn } from '@thf/ui/utils/cn';
import Image from 'next/image';
import portableTextStyles from './portable-text-styles.module.css';

type SanityImageType = {
  _type: string;
  alt: string;
  _key: string;
  asset: {
    _ref: string;
    _type: 'reference';
  };
  dimensions: {
    _type: 'sanity.imageDimensions';
    width: number;
    aspectRatio: number;
    height: number;
  };
};
const SanityImage = ({ asset, dimensions, alt }: SanityImageType) => {
  // console.log(dimensions);
  return (
    <div className='w-full border rounded-xl p-1 dark:border-gray-900 bg-gray-100 dark:bg-gray-900/50 '>
      <div className='flex w-full'>
        <Image
          className='w-full rounded-md'
          width={dimensions ? dimensions.width : 200}
          height={dimensions ? dimensions.height : 100}
          alt='Blog_img'
          src={urlForImage(asset)
            .height(dimensions ? dimensions.height : 1000)
            .width(dimensions ? dimensions.width : 2000)
            .url()}
          sizes='100vw'
        />
      </div>
      <div className=''>
        <em className='font-light text-xs px-2'>
          {alt ? `Alt : ${alt}` : 'image'}
        </em>
      </div>
    </div>
  );
};

const myPortableTextComponents: PortableTextComponents = {
  types: {
    image: ({ value }) => {
      return <SanityImage {...value} />;
    },
    // code: ({ value }) => {
    //   return (
    //     <div className='text-base'>
    //       <Code lang='tsx' className={`${SFMonoRegular.className}`}>
    //         {value.code}
    //       </Code>
    //     </div>
    //   );
    // },
  },
  block: {
    // Ex. 1: customizing common block types
    h1: ({ children }) => (
      <h1 className={` text-2xl mb-2 text-alt-100`}>{children}</h1>
    ),
    h2: ({ children }) => (
      <h2 className={` text-xl mb-2 text-alt-100`}>{children}</h2>
    ),
    h5: ({ children }) => (
      <div
        className={` mb-2 pt-1 p-4 border-2 rounded-xl text-alt-200 border-alt-300`}
      >
        <span className='text-alt-100'>Notes : </span>
        <span className=''>{children}</span>
      </div>
    ),
  },
  marks: {
    strong: ({ children }: { children: React.ReactNode }) => (
      <span className={`text-alt-100`}>{children}</span>
    ),
  },
};

export default async function ProjectsBody({
  content,
  className,
}: {
  content: any;
  className?: string;
}) {
  // console.log(content);
  return (
    <div
      className={cn(
        portableTextStyles.portableText,
        className,
        'md:max-w-[80vw] flex flex-col w-full'
      )}
    >
      <PortableText value={content} components={myPortableTextComponents} />
    </div>
  );
}
