.portableText {
  display: flex;
  width: auto;
  height: auto;
  flex-direction: column;
  font-size: 0.875rem;
}

.portableText ::selection {
  background-color: black;
}

.portableText h2 {
  margin-bottom: 0.25rem;
  margin-top: 0.5rem;
}

.portableText p {
  margin-bottom: 0.5rem;
  margin-top: 0.25rem;
}

.portableText li {
  font-weight: 500;
}

.portableText li::marker {
  font-size: 0.875rem;
}

.portableText strong {
  color: white;
}

.portableText blockquote {
  margin-bottom: 0.25rem;
  margin-top: 0.5rem;
  border-left-width: 2px;
  padding-left: 0.5rem;
  margin-left: 0.5rem;
  text-transform: capitalize;
}

.portableText ul {
  padding-left: 1.5rem;
  padding-top: 1rem;
}

.portableText ul > li {
  list-style-type: disc;
}

.portableText ol {
  padding-left: 1.5rem;
  padding-top: 1rem;
}

.portableText ol > li {
  list-style-type: decimal;
}

.portableText a {
  color: rgb(239, 68, 68);
  text-decoration-line: underline;
  transition-property: all;
  transition-timing-function: linear;
  transition-duration: 300ms;
}

.portableText a:hover {
  color: rgb(239, 68, 68);
}
