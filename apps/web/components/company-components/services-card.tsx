'use client';

import Image from 'next/image';
import { useTransform, motion, useScroll, MotionValue } from 'framer-motion';
import { useRef } from 'react';

interface Feature {
  id: number;
  title: string;
}

interface CardProps {
  id: number;
  icon: string;
  title: string;
  description: string;
  features: Feature[];
  progress: MotionValue<number>;
  range: [number, number];
  targetScale: number;
}

const Card: React.FC<CardProps> = ({
  id,
  icon,
  title,
  description,
  features,
  progress,
  range,
  targetScale,
}) => {
  const container = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start end', 'start start'],
  });

  const scale = useTransform(progress, range, [1, targetScale]);
  const opacity = useTransform(scale, [1, targetScale], [1, 0]);

  const blur = useTransform(scale, [1, targetScale], [0, 10]); // 0 to 10px blur

  const blurFilter = useTransform(progress, range, [
    'blur(0px)',
    id === 4 ? 'blur(0px)' : 'blur(10px)',
  ]);

  return (
    <div
      ref={container}
      className='md:h-[70dvh] h-[60dvh] flex items-center justify-center sticky top-0 md:top-20'
      style={{ top: `${id * 60}px` }}
    >
      <motion.div
        style={{ scale }}
        className='flex flex-col bg-white md:p-8 p-10 rounded-3xl shadow-2xl'
      >
        <div className='grid md:grid-cols-3 md:gap-20  h-[50dvh] md:h-[60dvh] max-w-7xl min-w-[300px] overflow-hidden'>
          <div className=''>
            <div className='md:pl-24flex-col md:pt-14  hidden md:flex'>
              <Image alt='' src={icon} className='h-[50dvh] w-auto' />
            </div>
            <div className='md:pl-24 flex h-40 flex-col md:pt-14 md:hidden'>
              <Image alt='' src={icon} className='w-auto h-40 ' />
            </div>
          </div>
          <div className='space-y-8 md:space-y-14 md:col-span-2 md:pr-20 flex-1 flex flex-col md:pt-4 justify-center items-center md:items-start '>
            <h3 className='md:text-8xl font-bold text-4xl text-accent-100 md:text-start text-center'>
              {title}
            </h3>
            <p className='md:text-lg text-sm/tight text-star min-w-[200px] md:w-full md:pr-20'>
              {description}
            </p>
            <div className='grid-cols-2 gap-1 hidden '>
              {features.map((f) => (
                <div
                  key={f.id}
                  className='flex items-center space-x-4 *:text-xs'
                >
                  <span className='w-8 h-3 bg-accent-100'></span>
                  <span>{f.title}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Card;
