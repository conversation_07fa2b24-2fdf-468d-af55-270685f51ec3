'use client';
import MediaComponent from '../../components/studio/media-component';
import ProjectsBody from '../../components/studio/post-body';
import { buttonVariants } from '@thf/ui/components/button';
import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { cn } from '@thf/ui/utils/cn'
import { brandproject } from '../../../../types/types';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import useMeasure from 'react-use-measure';
import {
  BlurIn,
  BlurInText,
} from '@thf/ui/animations/blur-in';

export function ProjectPageComponent({ project }: { project: brandproject }) {
  return (
    <div className=' z-20 flex flex-col pb-10 bg-white relative border-red-300'>
      {/* cover */}
      <div className='fixed top-20 left-4 z-50'>
        <ProjectNav title={project.title} />
      </div>
      <div className='fixed top-20 right-4 z-50'>
        <Link
          href={'/projects'}
          className='p-2 rounded-full bg-accent-100 hover:bg-black text-white flex flex-col items-center justify-center size-8 md:size-14'
        >
          <X strokeWidth={5} className='size-5' />
        </Link>
      </div>
      {/* hero sections */}
      <div className=' -z-30 hidden md:flex flex-col overflow-hidden fixed w-full h-dvh top-0'>
        <MediaComponent
          media={project.coverImage}
          autoplay={true}
          controls={true}
          className='rounded-none h-dvh object-cover '
        />
      </div>
      <div className=' -z-30 md:hidden flex-col flex  overflow-hidden fixed w-full h-dvh top-0'>
        <MediaComponent
          media={project.coverPortraitImage}
          autoplay={true}
          controls={true}
          className='rounded-none object-cover h-dvh'
        />
      </div>
      {/* intro */}
      <div className='z-20 bg-white p-3 space-y-3 mt-[96dvh] relative -mb-20 overflow-hidden rounded-t-3xl'>
        <div className='py-10 flex flex-col items-center justify-center p-6 mx-auto  max-w-[100vw]'>
          <p
            className={cn(
              FONT_CHAKRA_PETCH.className,
              'text-[2.8vw]  font-medium'
            )}
          >
            {project.intro}
          </p>
        </div>
        {/* row 1 */}
        {project.row_one && (
          <BlurIn className=''>
            <MediaComponent
              media={project.row_one}
              className='rounded-xl'
              autoplay={true}
              alowHover={true}
            />
          </BlurIn>
        )}
        {/* row 2 */}
        {project.row_two && (
          <BlurIn className=''>
            <MediaComponent
              media={project.row_two}
              className='rounded-xl'
              autoplay={true}
              alowHover={true}
            />
          </BlurIn>
        )}
        {/* row 3 */}
        {project.row_three_first_column && (
          <BlurIn className='grid grid-cols-2 gap-3 '>
            <MediaComponent
              media={project.row_three_first_column}
              className='rounded-xl'
              autoplay={true}
              alowHover={true}
            />
            <MediaComponent
              media={project.row_three_second_column}
              className='rounded-xl'
              autoplay={true}
              alowHover={true}
            />
          </BlurIn>
        )}
        {/* row 4 */}
        {project.row_four && (
          <BlurIn className=''>
            <MediaComponent
              media={project.row_four}
              className='rounded-xl'
              autoplay={true}
              alowHover={true}
            />
          </BlurIn>
        )}
        {/* row 5 */}
        {project.row_five_first_column && (
          <BlurIn className='grid grid-cols-2  h-full gap-3 '>
            <div className=' h-full'>
              <MediaComponent
                media={project.row_five_first_column}
                className='rounded-xl'
                autoplay={true}
                alowHover={true}
              />
            </div>
            <div className='flex flex-col h-full gap-3'>
              <div className='flex-1 h-full'>
                <MediaComponent
                  media={project.row_five_second_column_one}
                  className='rounded-xl object-cover'
                  autoplay={true}
                  alowHover={true}
                />
              </div>
              <div className='flex-1 h-full'>
                <MediaComponent
                  media={project.row_five_second_column_two}
                  className='rounded-xl object-cover'
                  autoplay={true}
                  alowHover={true}
                />
              </div>
            </div>
          </BlurIn>
        )}
        {/* row 6 */}
        {project.row_six && (
          <BlurIn className=''>
            <MediaComponent
              media={project.row_six}
              className='rounded-xl'
              autoplay={true}
              alowHover={true}
            />
          </BlurIn>
        )}
        <BlurIn className='py-20 flex flex-col w-full items-center justify-center'>
          {project.projectlink && (
            <Link
              href={project.projectlink}
              className={cn(
                FONT_CHAKRA_PETCH.className,
                buttonVariants({}),
                'bg-accent-100 md:text-4xl md:px-7 md:py-4 md:h-auto'
              )}
            >
              visit website
            </Link>
          )}
        </BlurIn>
        {/* footer */}
        <BlurIn className='p-4'>
          <div className='flex flex-col mx-auto w-full max-w-md pb-20'>
            <h2
              className={cn(
                FONT_CHAKRA_PETCH.className,
                'text-2xl font-bold text-accent-100'
              )}
            >
              The Story
            </h2>
            <ProjectsBody
              content={project.body}
              className='font-medium text-neutral-600 text-sm/tight md:text-lg/tight'
            />
          </div>
        </BlurIn>
      </div>
    </div>
  );
}

function ProjectNav({ title }: { title: brandproject['title'] }) {
  const [expand, setExpand] = useState(false);
  const [contentRef, { width, height }] = useMeasure();
  return (
    <div
      className={cn(
        FONT_CHAKRA_PETCH.className,
        'flex flex-row items-center w-fit bg-accent-100 hover:bg-black rounded-full py-2 px-4 cursor-pointer'
      )}
      onMouseEnter={() => setExpand(true)}
      onMouseLeave={() => setExpand(false)}
    >
      <div className='flex flex-row items-center w-fit space-x-2 font-medium text-white text-sm md:text-2xl'>
        <motion.div
          animate={{
            width: expand ? 240 : 0,
            opacity: expand ? 1 : 0,
          }}
          transition={{ type: 'just', bounce: 0.2, duration: 0.1 }}
          className={cn('flex flex-row items-center space-x-2')}
        >
          <Link href={'/'} className='hover:text-accent-100'>
            Home
          </Link>
          <span>→</span>
          <Link href={'/projects'} className='hover:text-accent-100'>
            Projects
          </Link>
          <span>→</span>
        </motion.div>
        <p
          ref={contentRef}
          className='whitespace-nowrap w-fit hover:text-accent-100'
        >
          {title}
        </p>
      </div>
    </div>
  );
}

export function AllProjectsCard({ p }: { p: brandproject[] }) {
  return (
    <div className='grid md:grid-cols-2 gap-3'>
      {p.map((b) => {
        return (
          <Link
            href={`/projects/${b.slug}`}
            key={b._id}
            className='flex flex-col w-full'
          >
            <BlurIn className='flex flex-col w-full h-full overflow-hidden rounded-xl'>
              <motion.div
                whileHover={{ scale: 1.06 }}
                className='h-[69vh] md:hidden'
                layoutId={`project-cover-img-${b.slug}`}
              >
                <MediaComponent
                  controls={true}
                  media={b.carouselPortrait}
                  className='rounded-xl object-cover h-[69vh]'
                />
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.06 }}
                className='h-[69vh] hidden md:flex'
                layoutId={`project-cover-img-${b.slug}`}
              >
                <MediaComponent
                  controls={true}
                  media={b.allProjectsLandscape}
                  className='rounded-xl object-cover h-[69vh]'
                />
              </motion.div>
            </BlurIn>
            <div className='flex flex-col pt-4 mb-10 pl-6'>
              <BlurInText
                className={cn(
                  FONT_CHAKRA_PETCH.className,
                  'text-xl font-medium text-accent-100'
                )}
              >
                {b.title}
              </BlurInText>
              <BlurInText
                className={cn('text-3xl font-semibold tracking-tighter')}
              >
                {b.subtitle}
              </BlurInText>
            </div>
          </Link>
        );
      })}
    </div>
  );
}
