'use client';
import { FONT_MONTSERRAT } from '@thf/constants/fonts';
import { cn } from '@thf/ui/utils/cn';
import hughie from '../../public/assets/imgs/logos/hughie_01.svg';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { TextEffect } from '@thf/ui/animations/text-effect';

export function LoadingAnimation() {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className='h-dvh w-dvw fixed top-0 left-0 bg-white z-600 overflow-hidden flex flex-col items-center justify-center'
          initial={{ opacity: 1, filter: 'blur(0px)' }}
          exit={{ opacity: 0, filter: 'blur(10px)' }}
          transition={{ duration: 0.5 }}
        >
          <section className=''>
            <h2
              className={cn(
                FONT_MONTSERRAT.className,
                'text-4xl leading-[0.8] md:text-[10.4vw]  text-accent-100 -tracking-[0.2rem] md:-tracking-[0.5vw] font-black md:leading-[8.4vw]'
              )}
            >
              <TextEffect per='char' as='span' preset='fade'>
                thehuefactory
              </TextEffect>
            </h2>
          </section>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
export function LoadingAnimation2() {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) return null;

  return (
    <main className='h-dvh w-dvw overflow-hidden flex flex-col items-center justify-center'>
      <div className='animate-bounce'>
        <Image alt='' src={hughie} className='md:max-w-[20%] w-full' />
      </div>
    </main>
  );
}
