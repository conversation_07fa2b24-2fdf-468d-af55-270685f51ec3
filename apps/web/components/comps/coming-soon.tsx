'use client';

import Image from 'next/image';
import logo_3D from '../../../public/assets/svgs/logo_3d_Shadow.svg';
import date from '../../../public/assets/svgs/date.svg';
import hughie from '../../../public/assets/svgs/hughie_01.svg';

import { motion } from 'framer-motion'
import { cn } from '@thf/ui/utils/cn';
import Link from 'next/link';
import { buttonVariants } from '@thf/ui/components/button';
import { FRAMER_MOTION_LIST_ITEM_VARIANTS } from '@thf/constants/framer-motion';

export default function ComingHome() {
  return (
    <BlurIn className='flex flex-col w-dvw overflow-hidden relative h-dvh items-center justify-center'>
      <Image alt='date' src={date} className=' w-32 md:w-48' />
      <div className='py-8 flex flex-col items-center space-y-4'>
        {/* <p className='text-xl font-bold'>Join Our Waitlist</p> */}
        <Link
          href={'/waitlist'}
          className={cn(buttonVariants({ variant: 'main', size: 'none' }))}
        >
          <span className=''>Join Our Waitlist</span>
        </Link>
      </div>

      <motion.div
        initial={'hidden'}
        animate={'show'}
        variants={FRAMER_MOTION_LIST_ITEM_VARIANTS}
        className='absolute bottom-10 left-4 md:left-10 '
      >
        <Image alt='logo_3D' src={logo_3D} className='w-16 md:w-24' />
      </motion.div>
      <motion.div
        initial={'hidden'}
        animate={'show'}
        variants={FRAMER_MOTION_LIST_ITEM_VARIANTS}
        transition={{ duration: 0.8 }}
        className='absolute -bottom-14 md:-bottom-20 md:-right-[10rem] -right-[6rem] '
      >
        <Image
          alt='hughie'
          src={hughie}
          className='-rotate-[28deg] w-[22rem] md:w-[38rem]'
        />
      </motion.div>
    </BlurIn>
  );
}

function BlurIn({
  children,
  className,
  duration,
}: {
  children: React.ReactNode;
  className?: string;
  duration?: number;
}) {
  const variants1 = {
    hidden: { filter: 'blur(10px)', opacity: 0 },
    visible: { filter: 'blur(0px)', opacity: 1 },
  };
  return (
    <motion.div
      initial='hidden'
      whileInView='visible'
      transition={{ duration: duration ? duration : 1 }}
      variants={variants1}
      viewport={{ once: true }}
      className={cn(className)}
    >
      {children}
    </motion.div>
  );
}
