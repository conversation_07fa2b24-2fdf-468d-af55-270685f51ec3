'use client';

import { useScroll, useTransform } from 'framer-motion';
import Image from 'next/image';
import { useRef } from 'react';

import hughie from '../../../public/assets/imgs/logos/hughie_01.svg';
import { content_text_lgog } from '../../lib/imgs';
export function HomePageParallax() {
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end start'],
  });

  const dy = useTransform(scrollYProgress, [0, 1], [0, 600]);
  const my = useTransform(scrollYProgress, [0, 1], [0, 200]);

  return (
    <div className='flex-col flex  w-full md:h-screen  bg-accent-100 z-10 -mb-10'>
      <div className=' w-full mx-auto p mt-16'>
        <Image alt='' src={content_text_lgog} className=' w-full' />
      </div>
      <div className='px-10 md:px-40'>
        <Image alt='' src={hughie} className='' />
      </div>
    </div>
  );
}
