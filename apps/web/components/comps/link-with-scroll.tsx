'use client';

import { buttonVariants } from '@thf/ui/components/button';
import { cn } from '@thf/ui/utils/cn';
import Link from 'next/link';

type props = {
  text: string;
  link: string;
  s: 'sm' | 'lg';
};

export function LinkScroll({ link, text, s }: props) {
  return s === 'lg' ? (
    <Link
      href={link}
      onClick={(e) => {
        e.preventDefault();
        document.querySelector(link)?.scrollIntoView({
          behavior: 'smooth',
        });
      }}
      className={cn(buttonVariants({ variant: 'main', size: 'none' }), '')}
    >
      <span>{text}</span>
    </Link>
  ) : s === 'sm' ? (
    <Link
      href={link}
      onClick={(e) => {
        e.preventDefault();
        document.querySelector(link)?.scrollIntoView({
          behavior: 'smooth',
        });
      }}
      className={cn(buttonVariants({ variant: 'main_sm', size: 'none' }), '')}
    >
      <span>{text}</span>
    </Link>
  ) : null;
}
