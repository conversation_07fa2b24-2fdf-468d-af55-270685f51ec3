'use client';

import { buttonVariants } from '@thf/ui/components/button';
import { join_us_h, join_us_hsm } from '../../lib/imgs';
import { cn } from '@thf/ui/utils/cn';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import astronaut from '../../public/assets/imgs/icons/astronaut.svg';
import { BlurIn } from '@thf/ui/animations/blur-in';
import { TextEffect } from '@thf/ui/animations/text-effect';

export function JoinUsHeaderAnimation() {
  return (
    <header className='h-[120vh] relative hidden md:flex flex-col'>
      <BlurIn className='flex flex-col w-full text-start  h-full space-y-4 absolute z-50 top-0 left-20'>
        <div className='flex flex-col w-full justify-start items-start pt-52 p-4 space-y-12 '>
          <p
            className={cn(
              FONT_CHAKRA_PETCH.className,
              'text-5xl  md:text-[12.4vw]  font-bold tracking-tighter md:leading-[9.4vw] text-white'
            )}
          >
            Join our
            <br />
            Space
          </p>
          {/* <h2>Join our Space.</h2> */}
          <p className='text-lg md:text-2xl text-white pt-14 pb-8'>
            <TextEffect per='char' as='span' preset='fade' delay={0.1}>
              No egos, just big ideas. You in?
            </TextEffect>
          </p>
          <Link
            href={'#joinusnow'}
            className={cn(
              buttonVariants({ variant: 'main', size: 'none' }),
              'hover:bg-white hover:text-accent-100'
            )}
            onClick={(e) => {
              e.preventDefault();
              document.querySelector('#joinusnow')?.scrollIntoView({
                behavior: 'smooth',
              });
            }}
          >
            <span>Join</span>
          </Link>
        </div>
      </BlurIn>
      <div className='absolute top-96 z-10 right-96'>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{
            opacity: 1,
            x: ['-200%', '100%'],
            y: [0, -20, 0],
            rotate: [0, 10, -10, 0],
          }}
          transition={{
            x: { duration: 8, ease: 'easeOut' },
            y: { delay: 2, duration: 5, repeat: Infinity, ease: 'easeInOut' },
            rotate: {
              delay: 2,
              duration: 5,
              repeat: Infinity,
              ease: 'easeInOut',
            },
          }}
        >
          <Image alt='astronaut' src={astronaut} className='w-16' />
        </motion.div>
      </div>
      <div className='absolute left-0 bottom-20 z-0'>
        <Image alt='join_us_img' src={join_us_h} className='' />
      </div>
    </header>
  );
}
export function JoinUsHeaderAnimationMobile() {
  return (
    <header className='h-dvh relative md:hidden'>
      <BlurIn className='flex flex-col w-full text-start justify-between h-dvh z-50'>
        {/* 1st section */}
        <div className='flex flex-col w-full justify-end pb-4 items-start p-4 pl-8 space-y-12 h-[30dvh] '>
          <h2
            className={cn(
              FONT_CHAKRA_PETCH.className,
              'text-7xl md:text-[12.4vw]   font-bold tracking-tighter md:leading-[9.4vw] text-white'
            )}
          >
            Join our
            <br />
            Space.
          </h2>
        </div>
        {/* 2nd section */}
        <div className='flex flex-col relative  h-[40dvh] -mb-10'>
          <div className='absolute top-44 left-32 z-50'>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{
                opacity: 1,
                x: ['-200%', '100%'],
                y: [0, -20, 0],
                rotate: [0, 10, -10, 0],
              }}
              transition={{
                x: { duration: 8, ease: 'easeOut' },
                y: {
                  delay: 2,
                  duration: 5,
                  repeat: Infinity,
                  ease: 'easeInOut',
                },
                rotate: {
                  delay: 2,
                  duration: 5,
                  repeat: Infinity,
                  ease: 'easeInOut',
                },
              }}
            >
              <Image alt='astronaut' src={astronaut} className='w-16' />
            </motion.div>
          </div>
          <div className=' absolute top-0 -right-5 z-30'>
            <Image alt='' src={join_us_hsm} className=' h-[40dvh] w-auto ' />
          </div>
        </div>
        {/* third section */}
        <div className='flex flex-col items-start w-full pb-8 h-[30dvh] -mt-10 px-4 pl-8'>
          <p className='text-lg md:text-2xl text-white pt-14 pb-8'>
            <TextEffect per='char' as='span' preset='fade' delay={0.1}>
              No egos, just big ideas. You in?
            </TextEffect>
          </p>
          <Link
            href={'#joinusnow'}
            className={cn(
              buttonVariants({ variant: 'main_sm', size: 'none' }),
              'hover:bg-white hover:text-accent-100'
            )}
            onClick={(e) => {
              e.preventDefault();
              document.querySelector('#joinusnow')?.scrollIntoView({
                behavior: 'smooth',
              });
            }}
          >
            <span>Join</span>
          </Link>
        </div>
      </BlurIn>
    </header>
  );
}
