import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { cn } from '@thf/ui/utils/cn';
import { constructMetadata } from '@thf/ui/utils/construct-metadata';
import { Viewport } from 'next';
import './globals.css';

export const metadata = constructMetadata();

export const viewport: Viewport = {
  themeColor: '#f5f5f5',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='en'>
      <head>
        <meta name='apple-mobile-web-app-capable' content='yes' />
        <meta
          name='apple-mobile-web-app-status-bar-style'
          content='black-translucent'
        />
      </head>
      <body className={cn('tracking-wider', FONT_CHAKRA_PETCH.className)}>
        {children}
      </body>
    </html>
  );
}
