import { ChevronRight } from 'lucide-react';
import Link from 'next/link';

export default function Page() {
  return (
    <main>
      <div className=''>
        {/* header */}
        <header className='h-[60dvh] home-bg-gradient white'>
          <div className='h-full w-full flex flex-col items-center justify-center'>
            <div>
              <h2 className='text-4xl font-bold text-white'>Approach</h2>
            </div>
            <div className='flex items-center space-x-2 text-sm my-2 font-medium'>
              <Link href={'/'} className='text-white'>
                Home
              </Link>
              <span>
                <ChevronRight className='h-5 w-5' />
              </span>
              <Link href={'/approach'}>Approach</Link>
            </div>
          </div>
        </header>
        {/* our services */}
        <section className='flex flex-col'>
          <span className='py-40'>our services</span>
          <span className='py-40'>our services</span>
          <span className='py-40'>our services</span>
        </section>
      </div>
    </main>
  );
}
