
import Image from 'next/image';
import LoadingLayout from '../../../components/comps/loading-layout';
import Footer from '../../../components/layouts/footer';
import { JoinUsHeaderAnimation, JoinUsHeaderAnimationMobile } from '../../../components/comps/join-us-components';
import JoinUsForm from '../../../components/forms/join';
import { content_alergic } from '../../../lib/imgs';

export default function Page() {
  return (
    <LoadingLayout>
      <main className='flex flex-col overflow-hidden relative z-10'>
        <div className='join-us-bg fixed h-screen inset-0 -z-30'></div>
        <JoinUsHeaderAnimation />
        <JoinUsHeaderAnimationMobile />
        {/* join us form */}
        <section
          id='joinusnow'
          className='flex flex-col md:min-h-screen rounded-t-3xl z-50 -mt-20 pt-28  p-4'
        >
          <div className='rounded-xl grid md:h-dvh md:grid-cols-4 gap-3 mb-40 '>
            <div className='h-full md:col-span-2 flex flex-col md:pl-12'>
              <JoinUsForm />
            </div>
            <div className='flex flex-col p-4 md:col-span-2 items-center mt-12'>
              <Image alt='' src={content_alergic} className='w-[24dvw]' />
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </LoadingLayout>
  );
}
