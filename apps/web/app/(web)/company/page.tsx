'use client';
import { ArrowDown } from 'lucide-react';
import Image from 'next/image';

import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { Button, buttonVariants } from '@thf/ui/components/button';
import { cn } from '@thf/ui/utils/cn';
import { motion, useInView, useScroll } from 'framer-motion';
import Link from 'next/link';
import { useRef } from 'react';
import Card from '../../../components/company-components/services-card';
import LoadingLayout from '../../../components/comps/loading-layout';
import Footer from '../../../components/layouts/footer';
import {
  company_desktop,
  company_mobile,
  start_desktop,
  start_mobile,
} from '../../../lib/imgs';
import { SERVICES_ARRAY } from '../../../lib/services-array';
import sign from '../../../public/assets/imgs/icons/sign.svg';

export default function Page() {
  const container = useRef(null);
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: true, amount: 0.8 });

  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start start', 'end end'],
  });
  return (
    <LoadingLayout>
      <main className='z-0'>
        <div className='flex flex-col w-full h-full z-0 relative'>
          {/* parallax */}
          <div className='flex-col flex fixed justify-between h-dvh  inset-0 -z-10 -mb-10'>
            <Image
              alt='company_home'
              src={company_desktop}
              className='h-dvh object-cover  w-full object-bottom hidden md:flex'
            />
            <Image
              alt='company_home'
              src={company_mobile}
              className='object-fill object-bottom h-dvh md:hidden'
            />
            <div className='absolute bottom-[16dvh] md:bottom-20 left-1/2 -translate-x-1/2'>
              <Button
                variant={'main_no_style'}
                size={'none'}
                onClick={(e) => {
                  e.preventDefault();
                  document.querySelector('#companypage')?.scrollIntoView({
                    behavior: 'smooth',
                  });
                }}
                className='p-2 animate-bounce duration-1000'
              >
                <ArrowDown className='size-16' />
              </Button>
            </div>
          </div>
          {/* content */}
          <div
            id='companypage'
            className='grid md:grid-cols-2 mt-[96dvh] rounded-t-3xl gap-4 w-full md:h-dvh -mb-10 z-10 md:overflow-hidden waitlist-sky-bg md:pt-[18dvh] md:pr-10 md:pl-10'
          >
            {/* img */}
            <div className='md:pl-24 flex-col order-last md:order-first hidden md:flex'>
              <motion.div
                initial={{ opacity: 0, y: 80 }}
                whileInView={{ opacity: 1, y: -80 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{
                  type: 'spring',
                  stiffness: 260,
                  damping: 20,
                  delay: 0.5,
                  duration: 1000,
                }}
              >
                <Image alt='' src={sign} className='md:w-[30dvw] ' />
              </motion.div>
            </div>
            {/* mobile */}
            <div className='md:pl-24 pt-20 flex flex-col order-last -mb-80 md:order-first md:hidden items-center justify-center'>
              <motion.div
                initial={{ opacity: 0, y: 80 }}
                whileInView={{ opacity: 1, y: -80 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{
                  type: 'spring',
                  stiffness: 260,
                  damping: 20,
                  delay: 0.5,
                  duration: 1000,
                }}
              >
                <Image
                  alt=''
                  src={sign}
                  className='w-[80dvw] max-h-[50rem] object-cover object-center'
                />
              </motion.div>
            </div>
            {/* content */}
            <div className='flex flex-col space-y-8 *:text-white *:text-sm/tight  md:*:text-lg  p-4 px-8 md:px-0 pt-14 md:pt-0 md:pl-10 md:pr-32 '>
              <p>
                We are a full-service creative company dedicated to helping
                brands thrive!
              </p>
              <p>
                thehuefactory was born out of a passion for creativity and
                technology. Our founder&apos;s journey started in childhood,
                with a deep love for art and imagination that grew alongside a
                formal education in technology. Merging these two worlds became
                the driving force behind the creation of thehuefactory a space
                where creative ideas meet cutting-edge digital solutions.
              </p>
              <p>
                Our brand is built on the belief that creativity is boundless,
                and every brand deserves a fresh, innovative approach. After
                conducting extensive research and surveys, including a landmark
                study in Ghana, we discovered that many brands struggled to
                stand out, often perceived as stagnant or uninspired. This
                finding ignited our vision to help brands break free from the
                ordinary and inject fresh energy into their identities.
              </p>
            </div>
          </div>
          {/* vision */}
          <div className='flex flex-col bg-accent-100 relative p-4 px-8 pt-32 z-40  -mb-10 rounded-3xl md:rounded-[2.8rem]'>
            <div className='grid md:grid-cols-2 gap-12 md:gap-4  md:px-40 md:pt-40'>
              <div className='flex flex-col space-y-4 items-start *:text-white'>
                <h3 className='text-4xl  font-bold text-start'>Our Vision</h3>
                <p className='text-sm/tight md:text-xl text-start'>
                  &quot; We envision a world where creativity is a force for
                  positive change, inspiring and connecting people across
                  cultures, industries, and disciplines.&quot;
                </p>
              </div>
              <div className='flex flex-col space-y-4 items-start *:text-white'>
                <h3 className='text-4xl  font-bold text-start'>Our Mission</h3>
                <p className='text-sm/tight md:text-xl text-left'>
                  &quot;We are committed to pushing the boundaries of creative
                  expression, fostering innovation, and delivering impactful
                  solutions that resonate with our clients and their audiences.
                  Our mission is to be the catalyst for transformative ideas,
                  seamlessly blending artistry with strategic thinking to shape
                  the future of communication and design.&quot;
                </p>
              </div>
            </div>
            <div className='pt-32 flex items-center justify-center text-center md:-mb-20'>
              <h3 className='text-5xl md:text-8xl font-bold text-white'>
                Services
              </h3>
            </div>
            <div
              ref={container}
              className='flex flex-col space-y-8 relative mt-[10dvh] md:mb-32'
            >
              {SERVICES_ARRAY.map((s, i) => {
                const targetScale = 1 - (SERVICES_ARRAY.length - i) * 0.05;
                return (
                  <Card
                    id={s.id}
                    icon={s.icon}
                    title={s.title}
                    description={s.description}
                    features={s.features}
                    key={`p_${i}`}
                    {...SERVICES_ARRAY}
                    progress={scrollYProgress}
                    range={[i * 0.25, 1]}
                    targetScale={targetScale}
                  />
                );
              })}
            </div>
          </div>
          <div className='w-full z-10 relative overflow-hidden h-dvh md:h-[120dvh]'>
            <Image
              alt='company_home'
              src={start_desktop}
              className='object-cover h-full w-full hidden md:flex '
            />
            <Image
              alt='company_home'
              src={start_mobile}
              className='object-cover -mb-16  h-dvh w-full md:hidden '
            />
            <div className='absolute top-24 md:top-56 left-10 flex flex-col  md:left-48'>
              <div className='flex flex-col items-start space-y-4 md:space-y-14'>
                <div className='flex flex-col space-y-6 md:space-y-12'>
                  <h2
                    className={cn(
                      FONT_CHAKRA_PETCH.className,
                      'text-6xl  md:text-[8.4vw] text-white leading-[0.8] font-bold tracking-tighter md:leading-[6.6vw]'
                    )}
                  >
                    Start your <br /> journey.
                  </h2>
                  <p className='text-lg md:text-2xl text-white'>
                    Send us a message.
                  </p>
                </div>
                <div className='hidden md:flex'>
                  <Link
                    href={'/contact'}
                    className={cn(
                      buttonVariants({ variant: 'main', size: 'none' })
                    )}
                  >
                    Contact
                  </Link>
                </div>
                <div className='md:hidden'>
                  <Link
                    href={'/contact'}
                    className={cn(
                      buttonVariants({ variant: 'main_sm', size: 'none' })
                    )}
                  >
                    Contact
                  </Link>
                </div>
              </div>
            </div>
          </div>
          {/* team */}
          <div className='w-full relative bg-accent-100 h-dvh rounded-3xl z-40 -mt-10 hidden'>
            <div className='flex flex-col items-center justify-center text-center py-10'>
              <h2 className='text-8xl font-bold text-white'> Team</h2>
            </div>
            <div></div>
          </div>
          <div className='w-full relative hidden bg-accent-50 h-[50dvh] z-10 -mt-10'>
            <div className='flex flex-col items-center justify-center text-center py-10'>
              <h2 className='text-8xl font-bold text-white'> .</h2>
            </div>
            <div></div>
          </div>
        </div>
      </main>
      <Footer />
    </LoadingLayout>
  );
}
