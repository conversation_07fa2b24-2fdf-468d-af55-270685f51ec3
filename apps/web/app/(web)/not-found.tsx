import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { cn } from '@thf/ui/utils/cn';
import Link from 'next/link';
import Footer from '../../components/layouts/footer';

export default function NotFound() {
  return (
    <div className={cn(FONT_CHAKRA_PETCH.className, 'font-sans')}>
      <div className='flex flex-col min-h-dvh w-full items-center justify-center'>
        <h2>Not Found</h2>
        <p>Could not find requested resource</p>
        <Link href='/'>Return Home</Link>
      </div>
      <Footer />
    </div>
  );
}
