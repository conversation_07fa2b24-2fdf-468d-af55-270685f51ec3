
import NotificationsEmail from '@thf/emails/web/notifications';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';
import { formSchema } from '../../../../components/forms/notifications';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const form: z.infer<typeof formSchema> = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      to: [form.email],
      subject: 'Welcome to theheuefactory.',
      text: '',
      react: NotificationsEmail(),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
// export async function POST(request: Request) {
//   const form: z.infer<typeof formSchema> = await request.json();
//   console.log('start');
//   try {
//     const data = await resend.emails.send({
//       from: 'Ekow | thehuefactory <<EMAIL>>',
//       to: ['<EMAIL>'],
//       bcc: ['<EMAIL>', '<EMAIL>'],
//       subject: 'Quote Document for Delaphone.',
//       // subject: 'Invoice For Portfolio Website Development.',
//       text: '',
//       react: ClientQuote(),
//       // react: Invoice(),
//     });
//     return NextResponse.json({ data });
//   } catch (error) {
//     console.log(error);
//     return NextResponse.json({ error });
//   }
// }
