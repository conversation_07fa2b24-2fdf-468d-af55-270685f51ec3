import ClientBriefAndInvoice from '@thf/emails/web/client-brief-and-invoice';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';
import { formSchema } from '../../../../components/forms/notifications';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const form: z.infer<typeof formSchema> = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: [form.email],
      subject: 'Briefs For Vine brands - Pazzl.',
      text: '',
      react: ClientBriefAndInvoice(),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
