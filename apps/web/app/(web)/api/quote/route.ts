import ClientQuote from '@thf/emails/web/quotes';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';
import { formSchema } from '../../../../components/forms/notifications';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const form: z.infer<typeof formSchema> = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Team | thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: [form.email],
      subject: 'Quote Document for Delaphone',
      text: '',
      react: ClientQuote(),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
