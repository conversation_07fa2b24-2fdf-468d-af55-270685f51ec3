
import { ContactAdmin, ContactClient } from '@thf/emails/web/contact';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';
import { contactFormSchema } from '../../../../components/forms/contact/contact';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const form: z.infer<typeof contactFormSchema> = await request.json();
  console.log('start');
  try {
    const client = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: [form.email],
      bcc: ['<EMAIL>'],
      subject: 'Your Message Has Been Submited.',
      react: ContactClient(form),
    });

    const admin = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      to: ['<EMAIL>', '<EMAIL>'],
      subject: `Contact Message from ${form.name}`,
      react: ContactAdmin(form),
    });
    return NextResponse.json({ client, admin });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
