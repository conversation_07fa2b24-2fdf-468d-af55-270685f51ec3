import { JoinUsAffiliates } from '@thf/emails/web/join-affiliate';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';
import { affiliateformSchema } from '../../../../components/forms/join';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const form: z.infer<typeof affiliateformSchema> = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: [form.email],
      bcc: ['<EMAIL>'],
      subject: 'Your Message Has Been Submited.',
      text: '',
      react: JoinUsAffiliates(form),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
