import { constructMetadata } from '@thf/ui/utils/construct-metadata';
import { Viewport } from 'next';
import React from 'react';

export const metadata = constructMetadata({
  title: 'Waitlist',
  description: 'Join our waitlist!',
});

export const viewport: Viewport = {
  themeColor: '#4DC4EB',
};

export default function WaitlistLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <>{children}</>;
}