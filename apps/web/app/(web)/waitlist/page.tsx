'use client';
import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { cn } from '@thf/ui/utils/cn';
import { motion } from 'framer-motion';
import Image from 'next/image';

import { TextEffect } from '@thf/ui/animations/text-effect';
import { useEffect, useState } from 'react';

import baloon from '../../../public/assets/svgs/BALLOON.svg';
import desktopbg from '../../../public/assets/imgs/bg/bg_desktop.jpg';
import mobilebg from '../../../public/assets/imgs/bg/bg_phone.jpg';

interface Position {
  x: number;
  y: number;
  rotation: number;
}

const generateRandomPositions = (count: number): Position[] => {
  const positions: Position[] = [];
  for (let i = 0; i < count; i++) {
    positions.push({
      x: Math.random() * 100 - 50, // Reduced range for more subtle movement
      y: Math.random() * 80 - 40, // Reduced range, with bias towards upward movement
      rotation: Math.random() * 20 - 10, // Slight rotation between -10 and 10 degrees
    });
  }
  return positions;
};

export default function Page() {
  const [randomPositions, setRandomPositions] = useState<Position[]>([]);

  useEffect(() => {
    setRandomPositions(generateRandomPositions(15)); // Increased number of positions for smoother animation
  }, []);

  // const lm = `We're relaunching soon!`;
  const lm = `Join Our Waitlist.`;

  return (
    <main className='flex flex-col h-dvh relative overflow-hidden z-10'>
      <div className='absolute inset-0 md:hidden'>
        <Image
          alt='mobile_bg'
          src={mobilebg}
          className='w-full h-full object-cover'
        />
      </div>
      <div className='absolute inset-0 hidden sm:block'>
        <Image
          alt='mobile_bg'
          src={desktopbg}
          className='w-screen object-bottom  h-dvh object-cover'
        />
      </div>
      {/* join us form */}
      <section
        id='joinusnow'
        className='flex flex-col h-full justify-between rounded-t-3xl z-50 relative'
      >
        <div className='pt-10 flex flex-col absolute top-0 items-center w-full mx-auto space-y-2  justify-end'>
          <div className='flex flex-col items-center text-center space-y-10'>
            <div className='flex flex-col -space-y-4'>
              <h2
                className={cn(
                  FONT_CHAKRA_PETCH.className,
                  'text-[28vw]  md:text-[12vw]  font-bold tracking-tighter leading-[3.6rem] md:leading-[6vw] text-white pt-6'
                )}
              >
                Good!
              </h2>
              <h2
                className={cn(
                  FONT_CHAKRA_PETCH.className,
                  'text-[13.5vw]  md:text-[5.5vw]  font-bold tracking-tighter leading-[3.6rem] md:leading-[6vw] text-white pt-6'
                )}
              >
                You&apos;re here.
              </h2>
            </div>
            <TextEffect
              per='char'
              preset='blur'
              delay={1.5}
              className='text-white pb-8'
            >
              {lm}
            </TextEffect>
            {/* <h2
              className={cn(
                FONT_CHAKRA_PETCH.className,
                'text-7xl  md:text-[8vw]  font-bold tracking-tighter leading-[3.6rem] md:leading-[6vw] text-white pt-6'
              )}
            >
              Join our <br /> waitlist
            </h2> */}
            <motion.div
              animate={{
                x: randomPositions.map((pos) => pos.x),
                y: randomPositions.map((pos) => pos.y),
                rotate: randomPositions.map((pos) => pos.rotation),
              }}
              transition={{
                duration: 40, // Longer duration for slower, more natural movement
                times: randomPositions.map(
                  (_, index) => index / (randomPositions.length - 1)
                ),
                repeat: Infinity,
                repeatType: 'reverse',
                ease: 'easeInOut',
              }}
              style={{ originY: 1 }} // Set rotation origin to bottom of balloon
              className='absolute bottom-[16rem] right-1/2 translate-x-1/3 md:bottom-[13rem] md:right-[27rem]'
            >
              <Image alt='baloon' src={baloon} className='w-[6.2rem] md:w-52' />
            </motion.div>
          </div>
          <div className='relative z-80'>
            <TextEffect
              per='char'
              preset='blur'
              delay={1.5}
              className='text-white pb-8'
            >
              Waitlist Closed
            </TextEffect>
          </div>
        </div>
      </section>
    </main>
  );
}
