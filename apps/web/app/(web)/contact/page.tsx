
import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { BlurIn } from '@thf/ui/animations/blur-in';
import { TextEffect } from '@thf/ui/animations/text-effect';
import { cn } from '@thf/ui/utils/cn';
import Image from 'next/image';
import LoadingLayout from '../../../components/comps/loading-layout';
import { LinkScroll } from '../../../components/comps/link-with-scroll';
import { content_chair, content_explode } from '../../../lib/imgs';
import ContactUsForm from '../../../components/forms/contact';
import Footer from '../../../components/layouts/footer';

export default function Page() {
  return (
    <LoadingLayout>
      <main className='flex flex-col bg-white'>
        <header className='pt-20 fixed bg-gray-100 flex flex-col w-full h-dvh top-0'>
          <BlurIn className='grid md:grid-cols-2 gap-4 relative'>
            <div className='flex flex-col w-full justify-start relative z-40 items-start pl-6 md:pl-20 p-4 md:space-y-12 pt-20'>
              <div className='flex flex-col space-y-2'>
                <h2
                  className={cn(
                    FONT_CHAKRA_PETCH.className,
                    'text-6xl leading-[0.8] md:text-[12.4vw]  font-bold tracking-tighter md:leading-[9.4vw]'
                  )}
                >
                  Get in
                  <br />
                  touch.
                </h2>
                <p className=' text-sm/tight md:text-2xl pt-8  md:pt-14 pb-8'>
                  <TextEffect per='char' as='span' preset='fade' delay={0.1}>
                    Lets start a conversation now.
                  </TextEffect>
                </p>
              </div>
              <div className='flex flex-col'>
                <div className='md:hidden mt-40'>
                  <LinkScroll s='sm' link='#contactcontent' text='Contact' />
                </div>
                <div className='md:flex hidden'>
                  <LinkScroll s='lg' link='#contactcontent' text='Contact' />
                </div>
              </div>
            </div>
            {/* mobile */}
            <div className=' md:hidden flex flex-col items-end justify-center pr-8 absolute top-[20%] -right-[8rem] '>
              <Image alt='' src={content_chair} className=' h-[36rem] w-auto' />
            </div>
            <div className='flex-col items-end justify-center pr-8 hidden md:flex'>
              <Image alt='' src={content_chair} className=' w-auto -mt-12' />
            </div>
          </BlurIn>
        </header>
        <section
          id='contactcontent'
          className='p-3 flex flex-col w-full bg-white relative z-50 mt-[90dvh] md:mt-[96dvh] overflow-hidden md:pt-28 rounded-t-3xl'
        >
          <div className='rounded-xl grid md:grid-cols-2 gap-3 p-4'>
            <div className='flex flex-col p-4 pl-14 order-last md:order-first items-center justify-center'>
              <Image
                alt=''
                src={content_explode}
                className='w-[80dvw] h-auto  md:w-[30dvw]'
              />
            </div>
            <div className='h-full flex flex-col rounded-lg md:pr-16'>
              <ContactUsForm />
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </LoadingLayout>
  );
}
