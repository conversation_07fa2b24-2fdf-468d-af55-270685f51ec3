import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { getBrandProjects } from '@thf/sanity/lib';
import { BlurIn } from '@thf/ui/animations/blur-in';
import { TextEffect } from '@thf/ui/animations/text-effect';
import { cn } from '@thf/ui/utils/cn';
import Image from 'next/image';
import { LinkScroll } from '../../../components/comps/link-with-scroll';
import LoadingLayout from '../../../components/comps/loading-layout';
import { content_overall } from '../../../lib/imgs';
import { AllProjectsCard } from '../../../components/comps/project-component';
import Footer from '../../../components/layouts/footer';

export const revalidate = 60;

export default async function Page() {
  const projects = await getBrandProjects();
  return (
    <LoadingLayout>
      <main className='z-0'>
        <header className='pt-20 fixed bg-[#ffd1c1] flex flex-col w-full h-dvh top-0'>
          <BlurIn className='grid md:grid-cols-2 gap-4 relative'>
            <div className='flex flex-col w-full justify-start relative z-40 items-start pl-6 md:pl-20 p-4 md:space-y-12 pt-20'>
              <div className='flex flex-col space-y-2'>
                <h2
                  className={cn(
                    FONT_CHAKRA_PETCH.className,
                    'text-6xl leading-[0.8] md:text-[10.4vw]  font-bold tracking-tighter md:leading-[8.4vw]'
                  )}
                >
                  Our
                  <br />
                  Projects.
                </h2>
                <p className=' text-sm/tight md:text-2xl pt-8 max-w-[200px] md:max-w-full  md:pt-14 pb-8'>
                  <TextEffect per='char' as='span' preset='fade' delay={0.1}>
                    Our expertise lies in developing brands that catalyze
                    positive change in corporate, consumer, and internal
                    environments.
                  </TextEffect>
                </p>
              </div>
              <div className='flex flex-col'>
                <div className='md:hidden mt-40'>
                  <LinkScroll s='sm' link='#allprojects' text='Explore' />
                </div>
                <div className='md:flex hidden'>
                  <LinkScroll s='lg' link='#allprojects' text='Explore' />
                </div>
              </div>
            </div>
            {/* mobile */}
            <div className=' md:hidden flex flex-col items-end justify-center pr-8 absolute -top-[18rem] -right-[18rem] '>
              <Image
                alt=''
                src={content_overall}
                className=' h-auto w-[32rem]'
              />
            </div>
            <div className='flex-col items-end justify-center pr-8 hidden md:flex'>
              <Image
                alt=''
                src={content_overall}
                className=' w-auto -mt-[24rem]'
              />
            </div>
          </BlurIn>
        </header>
        {/* our projects */}
        <section
          id='allprojects'
          className='p-3 flex flex-col w-full bg-white relative z-50 mt-[96dvh] overflow-hidden pt-28 rounded-t-3xl'
        >
          <AllProjectsCard p={projects} />
        </section>
      </main>
      <Footer />
    </LoadingLayout>
  );
}
