
import { getBrandProject, getBrandProjects } from '@thf/sanity/lib';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { pageProps } from '../../../../../../types/types';
import { constructMetadata } from '@thf/ui/utils/construct-metadata';
import { BASE_URL } from '@thf/constants/variables';
import { ProjectPageComponent } from '../../../../components/comps/project-component';

export const revalidate = 60;

export const dynamicParams = true; // or false, to 404 on unknown paths

export async function generateMetadata({
  params,
}: pageProps): Promise<Metadata> {
  const slug = (await params).slug;
  const p = await getBrandProject(slug);

  if (!p) {
    notFound();
  }
  return constructMetadata({
    title: `${p.title} | thehuefactory™`,
    description: p.subtitle,
    canonicalUrl: `${BASE_URL}/projects/${p.slug}`,
    // image: p.ogImage.url,
  });
}

export async function generateStaticParams() {
  const p = await getBrandProjects();
  if (!p) {
    notFound();
  }
  return p.map((post) => ({
    slug: post.slug,
  }));
}
export default async function Page({ params }: pageProps) {
  const slug = (await params).slug;
  const p = await getBrandProject(slug);
  if (!p) {
    notFound();
  }
  return <ProjectPageComponent project={p} />;
}
