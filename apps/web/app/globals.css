@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-diamond-grotesk), ui-sans-serif, system-ui,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';

  --color-backgroud: rgb(249, 249, 249);

  --color-accent-50: #ffddd1;
  --color-accent-100: #ff4200;
  --color-accent-200: #d53700;
  --color-accent-300: #7f2100;
  --color-accent-400: #3e1000;

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee var(--duration) linear infinite;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@utility home-bg-gradient {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/slider/elements/transparent.png');
  background-size: cover;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility join-us-bg {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/bg/space.png');
  background-size: cover;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility waitlist-sky-bg {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/bg/SKY.jpg');
  background-size: cover;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility waitlist-grass-bg {
  background-repeat: no-repeat;
  background-image: url('/assets/svgs/GRASS.svg');
  background-size: cover;
  background-position: bottom center;
  width: 100%;
  height: auto;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_h_desktop {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content_h.png');
  background-size: cover;
  background-position: top left;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_h_mobile {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content-bg.png');
  background-size: cover;
  object-position: left top;
  background-position: center center;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_w_desktop {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content_w.png');
  background-size: cover;
  background-position: center right;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility content_bg_w_mobile {
  background-color: rgb(249, 249, 249);
  background-repeat: no-repeat;
  background-image: url('/assets/imgs/content/content-bg-2.png');
  background-size: cover;
  object-position: left top;
  background-position: top right;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: inherit;
  z-index: 20;
}

@utility parallax {
  height: 48rem;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('/assets/imgs/bg/pricing.jpg');
}

@utility shadow-dark {
  background-color: var(--btn-dark-color);
  box-shadow:
    var(--btn-dark-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-dark-shadow-outer) 0px 0px 0px 1px;

  opacity: 1;

  &:hover {
    background-color: var(--btn-dark-hover-color);
    box-shadow:
      var(--btn-dark-shadow-inner) 0px 0px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-dark-shadow-outer) 0px 0px 0px 1px;

    opacity: 1;
  }
}

@utility shadow-light {
  /* light */
  background-color: var(--btn-white-color);
  box-shadow:
    var(--btn-white-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-white-shadow-outer) 0px 0px 0px 1px;

  opacity: 1;

  &:hover {
    background-color: var(--btn-white-hover-color);
    box-shadow:
      var(--btn-white-shadow-inner) 0px 0px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-white-shadow-outer) 0px 0px 0px 1px;

    opacity: 1;
  }
}

@layer utilities {
  :root {
    --btn-white-color: rgb(255, 255, 255);
    --btn-white-hover-color: rgb(248, 248, 248);
    --btn-white-shadow-inner: rgb(235, 235, 235);
    --btn-white-shadow-outer: rgb(235, 235, 235);

    --btn-dark-color: rgb(56, 56, 56);
    --btn-dark-hover-color: rgb(92, 92, 92);
    --btn-dark-shadow-inner: rgb(73, 73, 73);
    --btn-dark-shadow-outer: rgb(45, 45, 45);
  }
}

@layer base {
  html {
    background-color: rgb(249, 249, 249);
  }

  :root {
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}