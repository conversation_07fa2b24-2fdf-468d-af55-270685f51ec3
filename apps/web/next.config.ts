import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // typescript: {
  //   ignoreBuildErrors: true,
  // },
  // eslint: {
  //   ignoreDuringBuilds: true,
  // },
   images: {
    remotePatterns: [
      { hostname: 'cdn.sanity.io' },
      // { hostname: "source.unsplash.com" },
      // { hostname: "i.pinimg.com" },
    ],
    formats: ['image/webp'],
  },
};

export default nextConfig;
