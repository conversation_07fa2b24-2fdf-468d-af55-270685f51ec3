import VolunteerApplicationForm from '@/components/forms/volunteer-form';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import tag from '@/public/assets/svgs/tag.svg';
import Image from 'next/image';
export default function Page({ params }: { params: { id: string } }) {
  return (
    <main className='flex flex-col w-full min-h-dvh h-full bg-[#f7e1da]'>
      <div className='grid md:grid-cols-3 gap-4'>
        <div className='w-full md:col-span-2 px-2 flex flex-col md:pl-32 md:pr-20'>
          <div className='h-52 flex flex-col justify-end p-4'>
            <p
              className={cn(
                'text-6xl leading-[0.8] md:text-[6.4vw] text-center  font-bold tracking-tighter md:leading-[9.4vw]'
              )}
            >
              Volunteer's Form
            </p>
          </div>
          <section className='w-full '>
            <ScrollArea className='h-[calc(100dvh-14rem)] bg-white rounded-3xl'>
              <VolunteerApplicationForm id={params.id} />
              <ScrollBar orientation='vertical' />
            </ScrollArea>
          </section>
        </div>
        <section className='hidden md:flex'>
          <div className='sticky top-0 flex flex-col items-center'>
            <Image alt='tag-img' src={tag} className='h-[98dvh] w-auto' />
          </div>
        </section>
      </div>
    </main>
  );
}
