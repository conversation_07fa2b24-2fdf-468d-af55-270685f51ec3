'use client';

import { cn } from '@/lib/utils';
import { Toaster } from '@/components/ui/sonner';
import { FONT_CHAKRA_PETCH, FONT_DM_SANS } from '@/lib/constants';
import { AdminNav } from '@/components/dashboard/nav';
import { isSideOpened } from '@/lib/store/sidebar';
import { PanelRightClose, PanelRightOpen } from 'lucide-react';

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { isOpened, setIsOpened } = isSideOpened();
  return (
    <div className={cn(FONT_DM_SANS.className, 'flex w-full justify-between')}>
      <div className='z-[400]'>
        <AdminNav />
      </div>
      <div
        className={cn(
          'relative',
          isOpened
            ? 'w-[calc(100dvw-300px)]  min-w-[calc(100dvw-300px)] '
            : ' w-full'
        )}
      >
        <ToggleOpenSidebar />
        {children}
      </div>
      <Toaster position='bottom-right' />
    </div>
  );
}

function ToggleOpenSidebar() {
  const { isOpened, setIsOpened } = isSideOpened();
  return (
    <div className='absolute top-2 left-2 cursor-pointer'>
      {isOpened ? (
        <div onClick={() => setIsOpened(false)}>
          <PanelRightOpen />
        </div>
      ) : (
        <div onClick={() => setIsOpened(true)}>
          <PanelRightClose />
        </div>
      )}
    </div>
  );
}
