'use client';
import { AdminCharts } from '@/components/roles/admin/charts/admin-charts';
import { WaitlistsCharts } from '@/components/roles/admin/charts/waitlists-charts';
import { buttonVariants } from '@/components/ui/button';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { Database } from '@/lib/supabase/db/types';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import { useEffect, useState } from 'react';

import { AppSidebar } from '@/components/app-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';

type aps = Database['public']['Tables']['JOIN_US_TABLE']['Row'];
type wd = Database['public']['Tables']['waitlists']['Row'];

export default function Page() {
  const { profile, role } = useGetUser();

  const [applications, setApplications] = useState<aps[]>([]);
  const [waitlist, setWaitlist] = useState<wd[]>([]);
  const [col, setCol] = useState<aps | null>(null);

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('JOIN_US_TABLE')
      .select('*')
      .eq('email', profile?.email!)
      .single()
      .then(({ data }) => {
        if (data) setCol(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          if (
            payload.eventType === 'INSERT' ||
            payload.eventType === 'UPDATE'
          ) {
            const newData = payload.new as aps;
            if (newData.email === profile?.email) {
              setCol(newData);
            }
          }
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('JOIN_US_TABLE')
      .select('*')
      .then(({ data }) => {
        if (data) setApplications(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          setApplications((currentProducts) => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...currentProducts, payload.new as aps];
              case 'UPDATE':
                return currentProducts.map((product) =>
                  product.id === payload.new.id ? (payload.new as aps) : product
                );
              case 'DELETE':
                return currentProducts.filter(
                  (product) => product.id !== payload.old.id
                );
              default:
                return currentProducts;
            }
          });
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('waitlists')
      .select('*')
      .then(({ data }) => {
        if (data) setWaitlist(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'waitlists' },
        (payload) => {
          setWaitlist((currentProducts) => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...currentProducts, payload.new as wd];
              case 'UPDATE':
                return currentProducts.map((product) =>
                  product.id === payload.new.id ? (payload.new as wd) : product
                );
              case 'DELETE':
                return currentProducts.filter(
                  (product) => product.id !== payload.old.id
                );
              default:
                return currentProducts;
            }
          });
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const currentProfile = applications.find((a) => a.email === profile?.email);

  const ApprovedState = ({ value }: { value: boolean }) => {
    return (
      <div className={cn('flex items-center space-x-2')}>
        {value === true ? (
          <div className={cn('')}>
            <p>NDA Accepted Show the rest of the Dashboard Here</p>
          </div>
        ) : value == false ? (
          <div className={cn('')}>
            <p>NDA Not Accepted Kindly Log Out and NEVER come here again! </p>
          </div>
        ) : null}
      </div>
    );
  };
  const NullState = () => {
    return (
      <div className={cn('flex flex-col space-y-3')}>
        <p>NDA Needs To Be Signed</p>
        <Link
          href={`/dashboard/nda`}
          className={cn(buttonVariants({}), 'w-fit')}
        >
          {' '}
          View NDA
        </Link>
      </div>
    );
  };
  return (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      <main className='mx-auto w-full max-w-7xl p-2'>
        <p>{currentProfile?.is_nda_signed}</p>
        {role === 'Admin' ? (
          <section className='flex flex-col w-full items-center'>
            <AdminCharts users={applications} />
            <WaitlistsCharts users={waitlist} />
          </section>
        ) : role === 'Affiliate' ? (
          <section className='p-4'>
            <p className='text-xl font-medium'>Hey, {profile?.full_name}</p>
            <div className='flex'>
              {col?.is_nda_signed === null && <NullState />}
              {col?.is_nda_signed !== null && (
                <ApprovedState value={col?.is_nda_signed!} />
              )}
            </div>
          </section>
        ) : role === 'Collaborator' ? (
          <section className='p-4'>
            <p className='text-xl font-medium'>Hey, {profile?.full_name}</p>
            <div className='flex'>
              {col?.is_nda_signed === null && <NullState />}
              {col?.is_nda_signed !== null && (
                <ApprovedState value={col?.is_nda_signed!} />
              )}
            </div>
          </section>
        ) : (
          <section className='p-4'>
            <p className='text-xl font-medium'>Hey, {profile?.full_name}</p>
            <div className='flex'>
              {col?.is_nda_signed === null && <NullState />}
              {col?.is_nda_signed !== null && (
                <ApprovedState value={col?.is_nda_signed!} />
              )}
            </div>
          </section>
        )}
      </main>{' '}
    </div>
  );
}
