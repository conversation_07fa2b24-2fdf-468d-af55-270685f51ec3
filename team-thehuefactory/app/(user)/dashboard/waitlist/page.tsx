'use client';
import { WaitlistTable } from '@/components/roles/admin/tables/waitlist-table';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { Database } from '@/lib/supabase/db/types';
import { cn } from '@/lib/utils';
import { redirect } from 'next/navigation';
import { useEffect, useState } from 'react';

type wd = Database['public']['Tables']['waitlists']['Row'];

export default function Page() {
  const [waitlist, setWaitlist] = useState<wd[]>([]);
  const { profile, role } = useGetUser();

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('waitlists')
      .select('*')
      .then(({ data }) => {
        if (data) setWaitlist(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'waitlists' },
        (payload) => {
          setWaitlist((currentProducts) => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...currentProducts, payload.new as wd];
              case 'UPDATE':
                return currentProducts.map((product) =>
                  product.id === payload.new.id ? (payload.new as wd) : product
                );
              case 'DELETE':
                return currentProducts.filter(
                  (product) => product.id !== payload.old.id
                );
              default:
                return currentProducts;
            }
          });
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  if (role !== 'Admin') redirect('/dashboard');

  return (
    <main className='min-h-[38rem] p-4'>
      <div className='flex flex-col max-w-2xl mx-auto w-full p-4'>
        <h2 className={cn('text-4xl font-bold text-center')}>Waitlist</h2>
        <div>
          {!waitlist ? (
            <div>No Data</div>
          ) : (
            <div>
              <WaitlistTable data={waitlist} />
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
