'use client';
import ProposalForm from '@/components/forms/proposal';
import { ProposalTable } from '@/components/roles/affiliate/proposal-table';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { Database } from '@/lib/supabase/db/types/types';
import { redirect } from 'next/navigation';
import { useEffect, useState } from 'react';

type pd = Database['public']['Tables']['affiliate_proposals']['Row'];

export default function Page() {
  const [proposals, setProposals] = useState<pd[]>([]);
  const { profile, role } = useGetUser();

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('affiliate_proposals')
      .select('*')
      .eq('user_email', profile?.email!)
      .then(({ data }) => {
        if (data) setProposals(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'affiliate_proposals' },
        (payload) => {
          setProposals((currentProducts) => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...currentProducts, payload.new as pd];
              case 'UPDATE':
                return currentProducts.map((product) =>
                  product.id === payload.new.id ? (payload.new as pd) : product
                );
              case 'DELETE':
                return currentProducts.filter(
                  (product) => product.id !== payload.old.id
                );
              default:
                return currentProducts;
            }
          });
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  if (role !== 'Affiliate') redirect('/dashboard');
  return (
    <main className='mx-auto max-w-7xl w-full'>
      <section className='p-4 flex flex-col items-center w-full'>
        {!proposals ? (
          <div>No Data</div>
        ) : (
          <div>
            <ProposalTable data={proposals} />
          </div>
        )}
      </section>
      <section className='p-4 flex flex-col items-center w-full'>
        <ProposalForm />
      </section>
    </main>
  );
}
