'use client';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  currentDate,
  FONT_CHAKRA_PETCH,
  nextWeekWorkingDay,
} from '@/lib/constants';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { Database } from '@/lib/supabase/db/types';
import { cn } from '@/lib/utils';
import { ThumbsDown, ThumbsUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
type pd = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

export default function Page() {
  const { profile, role } = useGetUser();
  const [col, setCol] = useState<pd | null>(null);

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('JOIN_US_TABLE')
      .select('*')
      .eq('email', profile?.email!)
      .single()
      .then(({ data }) => {
        if (data) setCol(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          if (
            payload.eventType === 'INSERT' ||
            payload.eventType === 'UPDATE'
          ) {
            const newData = payload.new as pd;
            if (newData.email === profile?.email) {
              setCol(newData);
            }
          }
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  function onNdaSubmit(value: boolean) {
    return new Promise<pd | null>(async (resolve, reject) => {
      const supabase = supabaseClient();

      const { data, error } = await supabase
        .from('JOIN_US_TABLE')
        .update({
          is_nda_signed: value,
          nda_signed_date: `${currentDate} | ${nextWeekWorkingDay} `,
        })
        .eq('id', col?.id!)
        .select('*')
        .single();

      if (error) {
        reject(error);
      }
      resolve(data);
    });
  }

  const ApprovedState = ({ value }: { value: boolean }) => {
    return (
      <div className={cn('flex items-center space-x-2')}>
        {value === true ? (
          <div
            className={cn(
              buttonVariants(),
              'space-x-2 text-white bg-green-400'
            )}
          >
            <p>NDA Accepted</p>
            <ThumbsUp className='size-4' />
          </div>
        ) : value == false ? (
          <div
            className={cn(
              buttonVariants({ variant: 'destructive' }),
              'space-x-2'
            )}
          >
            <p>NDA Not Accepted</p>
            <ThumbsDown className='size-4' />
          </div>
        ) : null}
      </div>
    );
  };
  const NullState = () => {
    return (
      <div className={cn('flex items-center space-x-2')}>
        <Button
          icon={'both'}
          onClick={() => {
            toast.promise(onNdaSubmit(false), {
              loading: '...',
              success: (data) => `NDA not Accepted!`,
              error: (err) => `Error: ${err}`,
            });
          }}
        >
          <span>Decline</span>
          <ThumbsDown className='size-4' />
        </Button>
        <Button
          icon={'both'}
          onClick={() => {
            toast.promise(onNdaSubmit(true), {
              loading: '...',
              success: (data) => `NDA Accepted`,
              error: (err) => `Error: ${err}`,
            });
          }}
        >
          <span>Accept</span>
          <ThumbsUp className='size-4' />
        </Button>
      </div>
    );
  };
  return (
    <main className='mx-auto max-w-3xl w-full min-h-[38rem] p-4'>
      <section className={cn(FONT_CHAKRA_PETCH.className, 'py-10 space-y-8')}>
        <div>
          <p className='text-4xl font-bold'>NON-DISCLOSURE AGREEMENT</p>
        </div>
        <div className='flex flex-col space-y-8'>
          <p>
            This Non-Disclosure Agreement is entered into as of the Effective
            Date bg and between:
          </p>
          <div>
            <p>Disclosing Party</p>
            <p className='text-2xl font-bold'>Thehuefactory</p>
            <p className='text-accent-100'>Creative Agency</p>
            <p>Accra, Greater Accra, Ghana.</p>
          </div>
          <div>
            <p>Receiving Party</p>
            <p className='text-2xl font-bold'>{profile?.full_name!}</p>
            <p className='text-accent-100'>{role}</p>
            <p>Accra, Greater Accra, Ghana.</p>
          </div>
          <div>
            <p>Effective Date</p>
            <p>{nextWeekWorkingDay}</p>
          </div>
          <div>
            <p className='text-2xl font-bold'>1. Purpose</p>
            <p>
              The Disclosing Party intends to share certain confidential and
              proprietary information with the Receiving Party for the purpose
              of exploring, discussing, or engaging in a potential business
              relationship concerning the development of creative projects
              ("Purpose"). Both parties agree that the disclosure and protection
              of such Confidential Information is critical for the success of
              this relationship.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>2. Confidential Information</p>
            <p>
              For the purposes of this Agreement, "Confidential Information"
              includes but is not limited to:
            </p>
            <ol className='list-disc ml-4 *:marker:text-accent-100'>
              <li>Creative concepts, strategies, and plans.</li>
              <li>
                Technical and business information, including intellectual
                property.
              </li>
              <li>Pricing, project quotes, and budgets.</li>
              <li>Client Lists and contact information.</li>
              <li>Deliverables drafts and final project materials.</li>
              <li>
                Any other proprietary or sensitive information that is disclosed
                or becomes known through the course of collaboration.
              </li>
            </ol>
            <p>
              Confidential Information{' '}
              <strong className=''>does not include</strong> information that:
            </p>
            <p>
              Is publicly available at the time of disclosure or subsequently
              becomes publicly available through no breach of this Agreement by
              the Receiving Party;
            </p>
            <p>
              Is already in the possession of the Receiving Party without
              obligation of confidentiality at the time of disclosure;
            </p>
            <p>
              Is lawfully obtained from a third party without breach of
              confidentiality obligations;
            </p>
            <p>
              Is independently developed by the Receiving Party without the use
              of the Confidential Information.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>
              3. Obligations of the Receiving Party
            </p>
            <p>The Receiving Party agrees to:</p>

            <p>
              Maintain all Confidential Information in strict confidence and not
              disclose it to any third party without the prior written consent
              of the Disclosing Party;
            </p>
            <p>
              Use the Confidential Information solely for the Purpose stated in
              this Agreement;
            </p>
            <p>
              Take all reasonable precautions to protect the confidentiality of
              the information and prevent unauthorized use or disclosure.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>4. Term</p>
            <p>
              The obligations under this Agreement shall remain in effect for a
              period of 2 years from the Effective Date or until such time that
              the Confidential Information no longer qualifies as confidential
              under the terms of this Agreement, whichever occurs first.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>
              5. Return or Destruction of Confidential Information
            </p>
            <p>
              This Agreement is effective as of the Effective Date and shall
              remain in effect for a period of 2 gears from the date of
              disclosure of the Confidential Information. The obligations of
              confidentiality shall survive termination of this Agreement.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>6. No License</p>
            <p>
              Nothing in this Agreement grants the Collaborator any right or
              license to use the Confidential Information for any purpose other
              than the Purpose stated herein.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>7. Remedies</p>
            <p>
              Both parties acknowledge that any breach of this Agreement mag
              cause irreparable harm to the Disclosing Party and agree that the
              Disclosing Party shall have the right to seek injunctive relief,
              specific performance, or other equitable remedies to enforce this
              Agreement, in addition to ang other rights or remedies available
              at law.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>8. No Obligation to Proceed</p>
            <p>
              Nothing in this Agreement obligates either party to enter into any
              further agreement, business relationship, or collaboration.
            </p>
          </div>
          <div>
            <p>9. Governing Law</p>
            <p>
              This Agreement shall be governed by and construed in accordance
              with the laws of the Republic of Ghana, without regard to its
              conflict of law principles.
            </p>
          </div>
          <div>
            <p className='text-2xl font-bold'>1O. Entire Agreement</p>
            <p>
              This Agreement constitutes the entire understanding between the
              parties regarding the Confidential Information and supersedes all
              prior discussions, agreements, or understandings, whether written
              or oral.
            </p>
          </div>
          <div>
            <p>
              IN WITNESS WHEREOF, the Parties have executed this Non-Disclosure
              Agreement as of the Effective Date.
            </p>
          </div>
          <div className='grid md:grid-cols-2 gap-4'>
            <div className='flex  flex-col space-y-2'>
              <p className='text-xl font-bold'>Agency</p>
              <p>Name: Ekow Ekuma Hammond</p>
              <p>Title: Chief Ececutive Officer</p>
              <p>Date: {currentDate}</p>
            </div>
            <div className='flex  flex-col space-y-2'>
              <p className='text-xl font-bold'>{role}</p>
              <p>Name: {profile?.full_name!}</p>
              <p>Date: {currentDate}</p>
              <div className='flex'>
                {col?.is_nda_signed === null && <NullState />}
                {col?.is_nda_signed !== null && (
                  <ApprovedState value={col?.is_nda_signed!} />
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
