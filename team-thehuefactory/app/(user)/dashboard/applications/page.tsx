'use client';
import { ApplicationsTable } from '@/components/roles/admin/tables/application-table';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { Database } from '@/lib/supabase/db/types';
import { cn } from '@/lib/utils';
import { redirect } from 'next/navigation';
import { useEffect, useState } from 'react';

type aps = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

export default function Page() {
  const [applications, setApplications] = useState<aps[]>([]);
  const { profile, role } = useGetUser();

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('JOIN_US_TABLE')
      .select('*')
      .then(({ data }) => {
        if (data) setApplications(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          setApplications((currentProducts) => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...currentProducts, payload.new as aps];
              case 'UPDATE':
                return currentProducts.map((product) =>
                  product.id === payload.new.id ? (payload.new as aps) : product
                );
              case 'DELETE':
                return currentProducts.filter(
                  (product) => product.id !== payload.old.id
                );
              default:
                return currentProducts;
            }
          });
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  if (role !== 'Admin') redirect('/dashboard');

  return (
    <main className='min-h-[38rem] p-4'>
      <div>
        <div className='flex flex-col items-center w-full'>
          <div className='flex flex-col max-w-xl w-full p-4'>
            <h2 className={cn('text-4xl font-bold text-center')}>
              Applications
            </h2>
          </div>
        </div>
        <div>
          {!applications ? (
            <div>No Data</div>
          ) : (
            <div>
              <ApplicationsTable data={applications} />
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
