@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --btn-white-color: rgb(255, 255, 255);
  --btn-white-hover-color: rgb(248, 248, 248);
  --btn-white-shadow-inner: rgb(235, 235, 235);
  --btn-white-shadow-outer: rgb(235, 235, 235);

  --btn-dark-color: rgb(56, 56, 56);
  --btn-dark-hover-color: rgb(92, 92, 92);
  --btn-dark-shadow-inner: rgb(73, 73, 73);
  --btn-dark-shadow-outer: rgb(45, 45, 45);
}

@layer base {
  html {
    background-color: rgb(249, 249, 249);
  }

  :root {
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer components {
  .home-bg-gradient {
    background-color: rgb(249, 249, 249);
    background-repeat: no-repeat;
    background-image: url('/assets/imgs/slider/elements/transparent.png');
    background-size: cover;
    background-position: center center;
    width: 100%;
    height: 100%;
    opacity: 1;
    visibility: inherit;
    z-index: 20;
  }

  .join-us-bg {
    background-color: rgb(249, 249, 249);
    background-repeat: no-repeat;
    background-image: url('/assets/imgs/bg/space.png');
    background-size: cover;
    background-position: center center;
    width: 100%;
    height: 100%;
    opacity: 1;
    visibility: inherit;
    z-index: 20;
  }

  .content_bg_h_desktop {
    background-color: rgb(249, 249, 249);
    background-repeat: no-repeat;
    background-image: url('/assets/imgs/content/content_h.png');
    background-size: cover;
    background-position: top left;
    width: 100%;
    height: 100%;
    opacity: 1;
    visibility: inherit;
    z-index: 20;
  }

  .content_bg_h_mobile {
    background-color: rgb(249, 249, 249);
    background-repeat: no-repeat;
    background-image: url('/assets/imgs/content/content-bg.png');
    background-size: cover;
    object-position: left top;
    background-position: center center;
    width: 100%;
    height: 100%;
    opacity: 1;
    visibility: inherit;
    z-index: 20;
  }

  .content_bg_w_desktop {
    background-color: rgb(249, 249, 249);
    background-repeat: no-repeat;
    background-image: url('/assets/imgs/content/content_w.png');
    background-size: cover;
    background-position: center right;
    width: 100%;
    height: 100%;
    opacity: 1;
    visibility: inherit;
    z-index: 20;
  }

  .content_bg_w_mobile {
    background-color: rgb(249, 249, 249);
    background-repeat: no-repeat;
    background-image: url('/assets/imgs/content/content-bg-2.png');
    background-size: cover;
    object-position: left top;
    background-position: top right;
    width: 100%;
    height: 100%;
    opacity: 1;
    visibility: inherit;
    z-index: 20;
  }

  .parallax {
    height: 48rem;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/assets/imgs/bg/pricing.jpg');
  }

  .shadow-dark {
    background-color: var(--btn-dark-color);
    box-shadow:
      var(--btn-dark-shadow-inner) 0px -2.4px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-dark-shadow-outer) 0px 0px 0px 1px;

    opacity: 1;
  }

  .shadow-dark:hover {
    background-color: var(--btn-dark-hover-color);
    box-shadow:
      var(--btn-dark-shadow-inner) 0px 0px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-dark-shadow-outer) 0px 0px 0px 1px;

    opacity: 1;
  }

  /* light */
  .shadow-light {
    background-color: var(--btn-white-color);
    box-shadow:
      var(--btn-white-shadow-inner) 0px -2.4px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-white-shadow-outer) 0px 0px 0px 1px;

    opacity: 1;
  }

  .shadow-light:hover {
    background-color: var(--btn-white-hover-color);
    box-shadow:
      var(--btn-white-shadow-inner) 0px 0px 0px 0px inset,
      rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
      var(--btn-white-shadow-outer) 0px 0px 0px 1px;

    opacity: 1;
  }
}