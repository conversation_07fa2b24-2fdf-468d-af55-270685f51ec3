{"name": "collab-thehuefactory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@motionone/utils": "^10.18.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@react-email/components": "^0.0.25", "@supabase/ssr": "^0.5.1", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^11.11.4", "lenis": "^1.1.13", "lucide-react": "^0.460.0", "next": "14.2.15", "next-cloudinary": "^6.14.1", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-use-measure": "^2.1.1", "recharts": "^2.12.7", "resend": "^4.0.0", "sharp": "^0.33.5", "sonner": "^1.5.0", "styled-components": "^6.1.13", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "vaul": "^1.0.0", "zod": "^3.23.8", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@types/node": "^22.7.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "postcss": "^8.4.47", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.13", "typescript": "^5.6.3"}}