'use client';
import * as React from 'react';
import { GalleryVerticalEnd } from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { useRouter } from 'next/navigation';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { NavLinks } from './roles/constants';
import { toast } from 'sonner';
import { Database } from '@/lib/supabase/db/types';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { buttonVariants } from './ui/button';

type r = Database['public']['Enums']['Role Types'];
// This is sample data.

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const router = useRouter();
  const supabase = supabaseClient();
  const { user, role, profile } = useGetUser();

  const navlinks = NavLinks(role as r);
  const SignOut = async () => {
    await supabase.auth.signOut();
    router.refresh();
  };

  async function handleSignOut() {
    toast.promise(SignOut, {
      loading: 'Signing out..',
      success: (data) => {
        return 'Signed Out';
      },
      error: 'Error',
    });
    router.refresh();
  }
  return (
    <Sidebar variant='floating' {...props} className='bg-black '>
      {!user ? (
        <SidebarHeader className='bg-black'>
          <SidebarMenu>
            <SidebarMenuItem>
              <Link href={'/'} className='text-neutral-500 hover:text-black'>
                Go Back Home
              </Link>
              <Link href={'/'} className={cn(buttonVariants())}>
                Login
              </Link>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
      ) : (
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <p className='text-neutral-400'>{role}</p>
              <p className='hidden md:flex'>{user?.email!}</p>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
      )}
      {!user ? null : (
        <SidebarContent>
          <SidebarGroup>
            <SidebarMenu className='gap-2'>
              {!navlinks
                ? null
                : navlinks.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild>
                        <a href={item.url} className='font-medium'>
                          {item.title}
                        </a>
                      </SidebarMenuButton>
                      {/* {item.items?.length ? (
                      <SidebarMenuSub className='ml-0 border-l-0 px-1.5'>
                        {item.items.map((item) => (
                          <SidebarMenuSubItem key={item.title}>
                            <SidebarMenuSubButton
                              asChild
                              isActive={item.isActive}
                            >
                              <a href={item.url}>{item.title}</a>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    ) : null} */}
                    </SidebarMenuItem>
                  ))}
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>
      )}
    </Sidebar>
  );
}
