'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

import { CalendarIcon } from 'lucide-react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { format } from 'date-fns';

import { Calendar } from '@/components/ui/calendar';
import { Textarea } from '@/components/ui/textarea';
import { useEffect, useState } from 'react';
import { Database } from '@/lib/supabase/db/types';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { notFound, useRouter } from 'next/navigation';

const formSchema = z.object({
  dob: z.date({
    required_error: 'A date of birth is required.',
  }),
  interests: z
    .string()
    .min(10, { message: 'Please provide more details about your interests.' }),
  skills: z.string().optional(),
  areas: z.array(z.string()).refine((value) => value.length > 0, {
    message: 'Please select at least one area.',
  }),
  otherArea: z.string().optional(),
  whyJoin: z.string().min(10, {
    message: 'Please provide more details about why you want to join.',
  }),
  pastExperience: z.string().optional(),
  hoursPerWeek: z.enum(['less-than-5', '5-10', '10-15', '15-plus']),
  availableDays: z.array(z.string()).refine((value) => value.length > 0, {
    message: 'Please select at least one day.',
  }),
  preferredTime: z.enum(['morning', 'afternoon', 'evening']),
  equipment: z.string().optional(),
  additionalInfo: z.string().optional(),
  newsletter: z.enum(['yes', 'no']),
});

type ud = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

export default function VolunteerApplicationForm({ id }: { id: string }) {
  const supabase = supabaseClient();
  const [u, setU] = useState<ud | null>(null);
  const router = useRouter();
  const [submit, setSubmit] = useState('Submit Proposal');

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      areas: [],
      availableDays: [],
      hoursPerWeek: 'less-than-5',
      preferredTime: 'morning',
      newsletter: 'no',
    },
  });

  useEffect(() => {
    if (!id) return;

    supabase
      .from('JOIN_US_TABLE')
      .select('*')
      .eq('user_id', id)
      .single()
      .then(({ data, error }) => {
        if (error || !data) {
          setU(null);
          router.push('/');
        }
        setU(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          if (
            payload.eventType === 'INSERT' ||
            payload.eventType === 'UPDATE'
          ) {
            const newData = payload.new as ud;
            if (newData.user_id === id) {
              setU(newData);
            }
          }
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  function onSubmit(values: z.infer<typeof formSchema>) {
    return new Promise(async (resolve, reject) => {
      const d = format(values.dob, 'PPP');

      const n = values.newsletter === 'no' ? false : true;

      supabase
        .from('JOIN_US_TABLE')
        .update({
          dob: d,
          interests: values.interests,
          skills: values.skills,
          areas: values.areas,
          other_area: values.otherArea,
          why_join: values.whyJoin,
          past_experience: values.pastExperience,
          hours_per_week: values.hoursPerWeek,
          available_days: values.availableDays,
          preferred_time: values.preferredTime,
          equipment: values.equipment,
          additional_info: values.additionalInfo,
          newsletter: n,
          is_vol_form_submited: true,
        })
        .eq('user_id', id)
        .select('*')
        .single()
        .then(({ error, data }) => {
          if (error) reject(error);
          resolve(data);
        });
    });
  }

  return (
    <>
      {u?.is_vol_form_submited! === null && (
        <div className='p-4'>
          <p>Loading...</p>
        </div>
      )}
      {u?.is_vol_form_submited! === true && (
        <div className='flex flex-col p-8 text-center space-y-2'>
          <p className='text-neutral-400'>{u?.full_name!}</p>
          <p>Form Has Been Submitted</p>
        </div>
      )}
      {u?.is_vol_form_submited! === false && (
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((data) => {
              return toast.promise(onSubmit(data), {
                loading: 'Submitting',
                success: (data: any) => 'Application Sent',
                error: (err) => `Error: ${err.message}`,
              });
            })}
            className='space-y-8 p-4'
          >
            <div className='space-y-6'>
              <h2 className='text-2xl font-bold'>Personal Information</h2>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input value={u?.full_name!} disabled />
                  </FormControl>
                </div>
                <div>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input type='email' value={u?.email!} disabled />
                  </FormControl>
                </div>
                <div>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input type='tel' value={u?.phone!} disabled />
                  </FormControl>
                </div>
                <div>
                  <FormLabel>City / Location</FormLabel>
                  <FormControl>
                    <Input value={u?.location!} disabled />
                  </FormControl>
                </div>
                <FormField
                  control={form.control}
                  name='dob'
                  render={({ field }) => (
                    <FormItem className='flex flex-col'>
                      <FormLabel>Date of Birth</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-[240px] pl-3 rounded-lg text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className='w-auto p-0' align='start'>
                          <Calendar
                            mode='single'
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date('1900-01-01')
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className='space-y-6'>
              <h2 className='text-2xl font-bold'>About You</h2>
              <FormField
                control={form.control}
                name='interests'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      What interests you about volunteering with The Hue
                      Factory?
                    </FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='skills'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Do you have any relevant skills or experience in the
                      creative, technology or marketing field?
                    </FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='areas'
                render={() => (
                  <FormItem>
                    <FormLabel>
                      Which area(s) would you like to contribute to? (Select all
                      that apply)
                    </FormLabel>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                      {[
                        'Design (Graphic/Web)',
                        'Content Creation (Social Media, Blog)',
                        'Event Support',
                        'Community Engagement & Outreach',
                        'Branding Strategy',
                        'Photography/Videography',
                        'Other',
                      ].map((area) => (
                        <FormField
                          key={area}
                          control={form.control}
                          name='areas'
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={area}
                                className='flex flex-row items-start space-x-3 space-y-0'
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(area)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, area])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== area
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className='font-normal'>
                                  {area}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='otherArea'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      If you selected Other, please specify:
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='whyJoin'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Why do you want to be part of The Hue Factory?
                    </FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='pastExperience'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Describe any past volunteer experience or projects (if
                      applicable):
                    </FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='space-y-6'>
              <h2 className='text-2xl font-bold'>Availability</h2>
              <FormField
                control={form.control}
                name='hoursPerWeek'
                render={({ field }) => (
                  <FormItem className='space-y-3'>
                    <FormLabel>
                      How many hours per week can you commit to volunteering?
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className='flex flex-col space-y-1'
                      >
                        <FormItem className='flex items-center space-x-3 space-y-0'>
                          <FormControl>
                            <RadioGroupItem value='less-than-5' />
                          </FormControl>
                          <FormLabel className='font-normal'>
                            Less than 5 hours
                          </FormLabel>
                        </FormItem>
                        <FormItem className='flex items-center space-x-3 space-y-0'>
                          <FormControl>
                            <RadioGroupItem value='5-10' />
                          </FormControl>
                          <FormLabel className='font-normal'>
                            5-10 hours
                          </FormLabel>
                        </FormItem>
                        <FormItem className='flex items-center space-x-3 space-y-0'>
                          <FormControl>
                            <RadioGroupItem value='10-15' />
                          </FormControl>
                          <FormLabel className='font-normal'>
                            10-15 hours
                          </FormLabel>
                        </FormItem>
                        <FormItem className='flex items-center space-x-3 space-y-0'>
                          <FormControl>
                            <RadioGroupItem value='15-plus' />
                          </FormControl>
                          <FormLabel className='font-normal'>
                            15+ hours
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='availableDays'
                render={() => (
                  <FormItem>
                    <FormLabel>
                      What days are you available? (Select all that apply)
                    </FormLabel>
                    <div className='grid grid-cols-2 md:grid-cols-4 gap-2'>
                      {[
                        'Monday',
                        'Tuesday',
                        'Wednesday',
                        'Thursday',
                        'Friday',
                        'Saturday',
                        'Sunday',
                      ].map((day) => (
                        <FormField
                          key={day}
                          control={form.control}
                          name='availableDays'
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={day}
                                className='flex flex-row items-start space-x-3 space-y-0'
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(day)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, day])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== day
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className='font-normal'>
                                  {day}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='preferredTime'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred time of day:</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select preferred time' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='morning'>Morning</SelectItem>
                        <SelectItem value='afternoon'>Afternoon</SelectItem>
                        <SelectItem value='evening'>Evening</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='space-y-6'>
              <h2 className='text-2xl font-bold'>Additional Information</h2>
              <FormField
                control={form.control}
                name='equipment'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Do you have access to any equipment that could assist your
                      volunteer work?
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='e.g., laptop, camera, editing software, etc.'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='additionalInfo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Please share any other details you believe would be
                      relevant for us to know:
                    </FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='newsletter'
                render={({ field }) => (
                  <FormItem className='space-y-3'>
                    <FormLabel>
                      Would you like to be subscribed to our newsletter?
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className='flex flex-col space-y-1'
                      >
                        <FormItem className='flex items-center space-x-3 space-y-0'>
                          <FormControl>
                            <RadioGroupItem value='yes' />
                          </FormControl>
                          <FormLabel className='font-normal'>Yes</FormLabel>
                        </FormItem>
                        <FormItem className='flex items-center space-x-3 space-y-0'>
                          <FormControl>
                            <RadioGroupItem value='no' />
                          </FormControl>
                          <FormLabel className='font-normal'>No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button type='submit' className='w-full'>
              Submit Application
            </Button>
          </form>
        </Form>
      )}
    </>
  );
}
