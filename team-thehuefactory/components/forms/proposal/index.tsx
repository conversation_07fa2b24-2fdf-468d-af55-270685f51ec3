'use client';

import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useState } from 'react';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { userStore } from '@/lib/store/user';

export const proposalformSchema = z.object({
  proposal_type: z.string(),
  client_name: z.string().min(2, {
    message: 'Username must be at least 2 characters.',
  }),
  proposal_message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
});

export default function ProposalForm() {
  const [submit, setSubmit] = useState('Submit Proposal');

  const supabase = supabaseClient();
  const { user } = userStore();

  const form = useForm<z.infer<typeof proposalformSchema>>({
    resolver: zodResolver(proposalformSchema),
    defaultValues: {
      client_name: '',
      proposal_message: '',
      proposal_type: '',
    },
  });

  // 2. Define a submit handler.
  async function onSubmit(values: z.infer<typeof proposalformSchema>) {
    setSubmit('submitting...');

    const { error, status } = await supabase
      .from('affiliate_proposals')
      .insert({
        affiliate_proposal: values,
        user_email: user?.email!,
        user_id: user?.id!,
        completed: null,
        is_approved: null,
        is_recieved: null,
      });

    if (!error) {
      setSubmit('🎉 Proposal Sent...');
    } else {
      setSubmit('Try Again...');
    }
    form.reset();
  }
  return (
    <div className='w-full flex flex-col p-4 mx-auto max-w-lg'>
      <div className='p-4 flex flex-col space-y-2'>
        <p className='text-xl font-semibold'>
          Submit Your Referal Proposal Here
        </p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          {/* name and email */}
          <div className='grid sm:grid-cols-2 gap-4'>
            <FormField
              control={form.control}
              name='client_name'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder='Client/Company Name'
                      className='border rounded-lg h-auto w-full outline-none border-neutral-200 text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name='proposal_type'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl className='border rounded-lg h-auto w-full outline-none border-neutral-200 text-neutral-400'>
                      <SelectTrigger>
                        <SelectValue placeholder='Select Service' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className='border rounded-lg h-auto w-full outline-none border-neutral-200 text-neutral-400'>
                      <SelectItem value='Graphic Design'>
                        Graphic Design
                      </SelectItem>
                      <SelectItem value='Website Development'>
                        Website Development
                      </SelectItem>
                      <SelectItem value='App Development'>
                        App Development
                      </SelectItem>
                      <SelectItem value='App Development'>
                        Brand Development
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage className='text-xs' />
              </FormItem>
            )}
          />

          {/* textarea */}
          <div className='grid gap-4'>
            <FormField
              control={form.control}
              name='proposal_message'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder='Proposal Message'
                      className='border rounded-lg h-auto w-full outline-none border-neutral-200 text-neutral-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className='text-xs' />
                </FormItem>
              )}
            />
          </div>
          {/* submitbtn */}
          <div className='flex items-center justify-between w-full'>
            <Button variant={'outline'} type='submit'>
              <span>{submit}</span>
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
