'use client';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { useGetUser } from '@/lib/supabase/auth/getUser';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { redirect, usePathname, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button, buttonVariants } from '../ui/button';
import { NavLinks } from '../roles/constants';
import { Database } from '@/lib/supabase/db/types';
import { isSideOpened } from '@/lib/store/sidebar';

type r = Database['public']['Enums']['Role Types'];

export function AdminNav() {
  const router = useRouter();
  const pathname = usePathname();
  const supabase = supabaseClient();
  const { user, role, profile } = useGetUser();

  const { isOpened, setIsOpened } = isSideOpened();
  const navlinks = NavLinks(role as r);
  const SignOut = async () => {
    await supabase.auth.signOut();
    router.refresh();
  };

  async function handleSignOut() {
    toast.promise(SignOut, {
      loading: 'Signing out..',
      success: (data) => {
        return 'Signed Out';
      },
      error: 'Error',
    });
    router.refresh();
  }
  return (
    <div
      className={cn(
        'flex  fixed top-0 bottom-0 p-2 overflow-hidden',
        !isOpened ? 'w-0 hidden' : 'w-[300px]'
      )}
    >
      <div className='flex flex-col bg-black rounded-3xl p-2'>
        {!user ? (
          <div className='px-4 py-2 flex flex-col items-center w-full justify-between space-y-4  border-b border-neutral-400'>
            <Link href={'/'} className='text-white hover:text-accent-100'>
              Go Back Home
            </Link>
            <Link
              href={'/'}
              className={cn(buttonVariants({ variant: 'main' }), 'w-full')}
            >
              Login
            </Link>
          </div>
        ) : (
          <div className='flex flex-col h-full  justify-between w-full '>
            <div className='flex flex-col justify-between w-full p-2'>
              <div className='flex flex-col'>
                <p className=' text-white hover:text-accent-100'>{role}</p>
                <p className='hidden md:flex text-neutral-400 hover:text-accent-100'>
                  {user?.email!}
                </p>
                <p className='md:hidden flex text-neutral-400 hover:text-accent-100'>
                  {profile?.full_name!}
                </p>
              </div>
            </div>
            <div
              className={cn(
                'flex flex-col space-y-4 flex-1 border-t border-neutral-500 p-2',
                user.email !== profile?.email ? 'hidden' : 'flex'
              )}
            >
              {!navlinks
                ? null
                : navlinks.map((l) => (
                    <Link
                      href={l.url}
                      className={cn(
                        'text-white hover:text-accent-100 font-bold',
                        pathname === l.url && 'text-accent-100'
                      )}
                    >
                      {l.title}
                    </Link>
                  ))}
            </div>
            <div className='flex flex-col w-full'>
              {role === 'Admin' && (
                <div className={cn('flex flex-col w-full pb-4')}>
                  <Link
                    href={'/studio'}
                    className={cn(
                      buttonVariants({ variant: 'main' }),
                      'bg-white text-black hover:bg-white hover:text-accent-100'
                    )}
                  >
                    Studio
                  </Link>
                </div>
              )}
              <div className='pt-4 border-t border-neutral-400'>
                <Button
                  variant={'main'}
                  onClick={handleSignOut}
                  className='w-full'
                >
                  Log Out
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
