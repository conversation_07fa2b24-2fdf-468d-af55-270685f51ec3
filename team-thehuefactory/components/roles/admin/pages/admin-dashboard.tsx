'use client';

import { supabaseClient } from '@/lib/supabase/auth/client';
import { Database } from '@/lib/supabase/db/types';
import { useEffect, useState } from 'react';
import { AdminCharts } from '../charts/admin-charts';

type aps = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

export default function AdminPage() {
  const [applications, setApplications] = useState<aps[]>([]);

  useEffect(() => {
    const supabase = supabaseClient();

    supabase
      .from('JOIN_US_TABLE')
      .select('*')
      .then(({ data }) => {
        if (data) setApplications(data);
      });

    const channel = supabase
      .channel('custom-all-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          setApplications((currentProducts) => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...currentProducts, payload.new as aps];
              case 'UPDATE':
                return currentProducts.map((product) =>
                  product.id === payload.new.id ? (payload.new as aps) : product
                );
              case 'DELETE':
                return currentProducts.filter(
                  (product) => product.id !== payload.old.id
                );
              default:
                return currentProducts;
            }
          });
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return (
    <main className='flex flex-col items-center'>
      <AdminCharts users={applications} />
    </main>
  );
}

// @pass = Theadmin@TheHueFactory#!
