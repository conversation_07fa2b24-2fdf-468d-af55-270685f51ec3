'use client';

import * as React from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal, ThumbsDown, ThumbsUp } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { format, parseISO } from 'date-fns';
import { Button, buttonVariants } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Database } from '@/lib/supabase/db/types';

import { toast } from 'sonner';
import * as z from 'zod';
import { useRouter } from 'next/navigation';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { cn } from '@/lib/utils';
import { formatGhanaPhone } from '@/lib/constants/gh-phone';
import Link from 'next/link';
import { Drawer } from 'vaul';

const FormSchema = z.object({
  completed: z.boolean().nullable(),
});

type applicationsData = Database['public']['Tables']['JOIN_US_TABLE']['Row'];
type is_reviewed = Database['public']['Enums']['is_reviewed'];
type is_approved = Database['public']['Enums']['is_accepted'];

export function ApplicationsTable({ data }: { data: applicationsData[] }) {
  const router = useRouter();

  const onReviewedSubmit = (value: is_reviewed, user: applicationsData) => {
    return new Promise<applicationsData | null>(async (resolve, reject) => {
      const supabase = supabaseClient();

      const role = user.join_role;

      const { data, error } = await supabase
        .from('JOIN_US_TABLE')
        .update({
          reviewed: value,
        })
        .eq('id', user?.id!);

      if (error) {
        reject(error);
      }

      if (value === 'reviewed') {
        if (role === 'Affiliate') {
          await fetch('/api/affiliate/reviewed', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });
        } else if (role === 'Collaborator') {
          await fetch('/api/collaborator/reviewed', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });
        } else if (role === 'Volunteer') {
          await fetch('/api/volunteer/reviewed', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });
        } else null;
      } else if (value === 'notAccepted') {
        const { data, error } = await supabase
          .from('JOIN_US_TABLE')
          .update({
            approved: 'notAccepted',
          })
          .eq('id', user?.id!)
          .select('*')
          .single();

        if (error) {
          reject(error);
        }
        if (error) {
          reject(error);
        } else {
          if (role === 'Affiliate') {
            await fetch('/api/affiliate/not-accepted', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });
          } else if (role === 'Collaborator') {
            await fetch('/api/collaborator/not-accepted', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });
          } else if (role === 'Volunteer') {
            await fetch('/api/volunteer/not-accepted', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });
          } else null;
        }
      } else null;
      resolve(data);
    });
  };
  const onApprovedSubmit = (value: is_approved, user: applicationsData) => {
    return new Promise<applicationsData | null>(async (resolve, reject) => {
      const supabase = supabaseClient();

      const role = user.join_role;
      const affiliatePass = `${user.full_name?.slice(0, 3)}#@Aff!`;
      const collaboratorPass = `${user.full_name?.slice(0, 3)}#@Col!`;
      const volunteerPass = `${user.full_name?.slice(0, 3)}#@Vol!`;

      if (user.reviewed === 'received') {
        reject('Application Not Reviewed');
      } else {
        const { data, error } = await supabase
          .from('JOIN_US_TABLE')
          .update({
            approved: value,
          })
          .eq('id', user?.id!)
          .select('*')
          .single();

        if (error) {
          reject(error);
        }

        if (value === 'accepted') {
          if (role === 'Affiliate') {
            const nd = { ...user, pass: affiliatePass };
            const { data: newData, error: newError } =
              await supabase.auth.signUp({
                email: user.email!,
                password: affiliatePass,
                options: {
                  data: {
                    full_name: user.full_name,
                  },
                },
              });
            if (error || newError) {
              reject(error || newError);
            } else {
              await supabase
                .from('profiles')
                .update({
                  full_name: user.full_name,
                  email: user.email,
                  role: user.join_role,
                })
                .eq('id', newData.user?.id!)
                .then(({ error }) => {
                  if (error) {
                    reject('db:' + error);
                  }
                });
              await fetch('/api/affiliate/approved', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(nd),
              });
            }
          } else if (role === 'Collaborator') {
            const cnd = { ...user, pass: collaboratorPass };
            const { data: newData, error: newError } =
              await supabase.auth.signUp({
                email: user.email!,
                password: collaboratorPass,
                options: {
                  data: {
                    full_name: user.full_name,
                  },
                },
              });
            if (error || newError) {
              reject(error?.message || newError?.message);
            } else {
              await supabase
                .from('profiles')
                .update({
                  full_name: user.full_name,
                  email: user.email,
                  role: user.join_role,
                })
                .eq('id', newData.user?.id!)
                .then(({ error }) => {
                  if (error) {
                    reject('db:' + error.message);
                  }
                });
              await fetch('/api/collaborator/approved', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(cnd),
              });
            }
          } else if (role === 'Volunteer') {
            const vnd = { ...user, pass: volunteerPass };
            const { data: newData, error: newError } =
              await supabase.auth.signUp({
                email: user.email!,
                password: volunteerPass,
                options: {
                  data: {
                    full_name: user.full_name,
                  },
                },
              });
            if (error || newError) {
              reject(error?.message || newError?.message);
            } else {
              await supabase
                .from('profiles')
                .update({
                  full_name: user.full_name,
                  email: user.email,
                  role: user.join_role,
                })
                .eq('id', newData.user?.id!)
                .then(({ error }) => {
                  if (error) {
                    reject('db:' + error.message);
                  }
                });
              await fetch('/api/volunteer/approved', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(vnd),
              });
            }
          } else null;
        } else if (value === 'notAccepted') {
          if (role === 'Affiliate') {
            await fetch('/api/affiliate/not-accepted', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });
          } else if (role === 'Collaborator') {
            await fetch('/api/collaborator/not-accepted', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });
          } else if (role === 'Volunteer') {
            await fetch('/api/volunteer/not-accepted', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });
          } else null;
        } else null;
        resolve(data);
      }
    });
  };

  const columns: ColumnDef<applicationsData>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value: any) =>
            table.toggleAllPageRowsSelected(!!value)
          }
          aria-label='Select all'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value: any) => row.toggleSelected(!!value)}
          aria-label='Select row'
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'created_at',
      header: 'Created Date',
      cell: ({ row }) => {
        const dateString: string = row.getValue('created_at');
        const date = parseISO(dateString);
        const formatted = format(date, 'do MMM yyyy');
        return <div className='capitalize'>{formatted}</div>;
      },
    },
    {
      accessorKey: 'full_name',
      header: 'Full Name',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className='whitespace-nowrap'>
            {/* <Dialog>
              <DialogTrigger>
                <p>
                  {row.getValue('full_name')} - <span>View User Details</span>
                </p>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Application Details</DialogTitle>
                  <DialogDescription>
                    {user.join_role === 'Volunteer' ? (
                      <UserTableDtails u={user} />
                    ) : user.join_role === 'Affiliate' ? (
                      <UserTableDtails u={user} />
                    ) : user.join_role === 'Collaborator' ? (
                      <UserTableDtails u={user} />
                    ) : null}
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog> */}
            {/* vaul */}
            {/* desktop */}
            <Drawer.Root direction='right'>
              <Drawer.Trigger className='relative flex h-10 flex-shrink-0 items-center justify-center '>
                {row.getValue('full_name')} - <span>View User Details</span>
              </Drawer.Trigger>
              <Drawer.Portal>
                <Drawer.Overlay className='fixed inset-0 bg-black/40' />
                <Drawer.Content
                  className='right-2 top-2 bottom-2 fixed z-10 outline-none w-[310px] flex overflow-hidden'
                  // The gap between the edge of the screen and the drawer is 8px in this case.
                  style={
                    {
                      '--initial-transform': 'calc(100% + 8px)',
                    } as React.CSSProperties
                  }
                >
                  <div className='bg-zinc-50 h-full w-full grow flex flex-col rounded-[16px]'>
                    <ScrollArea>
                      <div className='max-w-md mx-auto p-5'>
                        <Drawer.Title className='font-medium mb-2 text-zinc-900'>
                          Application Details
                        </Drawer.Title>
                        <Drawer.Description className='text-zinc-600 mb-2'>
                          {user.join_role === 'Volunteer' ? (
                            <UserTableDtails u={user} />
                          ) : user.join_role === 'Affiliate' ? (
                            <UserTableDtails u={user} />
                          ) : user.join_role === 'Collaborator' ? (
                            <UserTableDtails u={user} />
                          ) : null}
                        </Drawer.Description>
                      </div>
                    </ScrollArea>
                  </div>
                </Drawer.Content>
              </Drawer.Portal>
            </Drawer.Root>
          </div>
        );
      },
    },
    {
      accessorKey: 'phone',
      header: 'Phone Number',
      cell: ({ row }) => {
        return (
          <div className='whitespace-nowrap'>
            <span className='p-2 '>
              {formatGhanaPhone(row.getValue('phone'))}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'email',
      header: 'Email',
      cell: ({ row }) => {
        return (
          <div className='whitespace-nowrap'>
            <p>{row.getValue('email')}</p>
          </div>
        );
      },
    },

    {
      accessorKey: 'position',
      header: 'Position',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className='whitespace-nowrap'>
            {!row.getValue('position') ? (
              <p>No Position</p>
            ) : (
              <p>
                {row.getValue('position') === 'Other'
                  ? user.position_other
                  : row.getValue('position')}
              </p>
            )}
          </div>
        );
      },
    },

    {
      accessorKey: 'resume_url',
      header: () => (
        <div className='flex w-full items-center justify-center'>
          <p>Resume/CV</p>
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div className='flex w-full flex-col items-center text-center whitespace-nowrap'>
            {!row.getValue('resume_url') ? (
              <p>No Resume</p>
            ) : (
              <Link href={row.getValue('resume_url')} target='_blank'>
                Open Resume
              </Link>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'join_role',
      header: () => (
        <div className='flex w-full items-center justify-center'>
          <p>Role</p>
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div className='flex w-full flex-col items-center text-center whitespace-nowrap'>
            <p>{row.getValue('join_role')}</p>
          </div>
        );
      },
    },
    {
      accessorKey: 'reviewed',
      header: () => (
        <div className='flex w-full items-center justify-center'>
          <p>Reviewed</p>
        </div>
      ),
      cell: ({ row }) => {
        const user = row.original;
        const id = user.user_id;
        const value: is_reviewed = row.getValue('reviewed');

        const ApprovedState = ({ value }: { value: is_reviewed }) => {
          return (
            <div className={cn('flex items-center space-x-2')}>
              {value === 'reviewed' ? (
                <div
                  className={cn(
                    buttonVariants(),
                    'space-x-2 text-white bg-green-400'
                  )}
                >
                  <p>Reviewed</p>
                  <ThumbsUp className='size-4' />
                </div>
              ) : value == 'notAccepted' ? (
                <div
                  className={cn(
                    buttonVariants({ variant: 'destructive' }),
                    'space-x-2'
                  )}
                >
                  <p>Not Approved</p>
                  <ThumbsDown className='size-4' />
                </div>
              ) : null}
            </div>
          );
        };
        const NullState = () => {
          return (
            <div className={cn('flex items-center space-x-2')}>
              <Button
                onClick={() => {
                  toast.promise(onReviewedSubmit('notAccepted', user!), {
                    loading: 'Unapproving...',
                    success: (data) =>
                      `${data?.full_name!}'s Application is not Accepted!`,
                    error: (err) => `Error: ${err}`,
                  });
                }}
              >
                <ThumbsDown className='size-4' />
              </Button>
              <Button
                onClick={() => {
                  toast.promise(onReviewedSubmit('reviewed', user!), {
                    loading: 'Approving...',
                    success: (data) =>
                      `Reviewed ${data?.full_name!}'s Application`,
                    error: (err) => `Error: ${err}`,
                  });
                }}
              >
                <ThumbsUp className='size-4' />
              </Button>
            </div>
          );
        };

        return (
          <div className='flex items-center justify-center'>
            {value === 'received' && <NullState />}
            {value !== 'received' && <ApprovedState value={value} />}
          </div>
        );
      },
    },
    {
      accessorKey: 'approved',
      header: () => (
        <div className='flex w-full items-center justify-center'>
          <p>Approved</p>
        </div>
      ),
      cell: ({ row }) => {
        const user = row.original;
        const id = user.user_id;
        const value: is_approved = row.getValue('approved');

        const ApprovedState = ({ value }: { value: is_approved }) => {
          return (
            <div className={cn('flex items-center space-x-2')}>
              {value === 'accepted' ? (
                <div
                  className={cn(
                    buttonVariants(),
                    'space-x-2 text-white bg-green-400'
                  )}
                >
                  <p>Approved</p>
                  <ThumbsUp className='size-4' />
                </div>
              ) : value === 'notAccepted' ? (
                <div
                  className={cn(
                    buttonVariants({ variant: 'destructive' }),
                    'space-x-2'
                  )}
                >
                  <p>Not Approved</p>
                  <ThumbsDown className='size-4' />
                </div>
              ) : null}
            </div>
          );
        };
        const NullState = () => {
          return (
            <div className={cn('flex items-center space-x-2')}>
              <Button
                onClick={() => {
                  toast.promise(onApprovedSubmit('notAccepted', user!), {
                    loading: 'Unapproving...',
                    success: (data) =>
                      `${data?.full_name!}'s Application is not Accepted!`,
                    error: (err) => `Error: ${err}`,
                  });
                }}
              >
                <ThumbsDown className='size-4' />
              </Button>
              <Button
                onClick={() => {
                  toast.promise(onApprovedSubmit('accepted', user!), {
                    loading: 'Approving...',
                    success: (data) =>
                      `Reviewed ${data?.full_name!}'s Application`,
                    error: (err) => `Error: ${err}`,
                  });
                }}
              >
                <ThumbsUp className='size-4' />
              </Button>
            </div>
          );
        };

        return (
          <div className='flex items-center justify-center'>
            {value === 'reviewing' && <NullState />}
            {value !== 'reviewing' && <ApprovedState value={value} />}
          </div>
        );
      },
    },

    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const user = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(user.user_id!)}
              >
                Copy User ID
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className='w-full'>
      <div className='flex items-center justify-center py-4'>
        <Input
          placeholder='Filter events..'
          value={(table.getColumn('email')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('email')?.setFilterValue(event.target.value)
          }
          className='max-w-sm rounded-xl'
        />
      </div>
      <div className='rounded-md border'>
        <ScrollArea className='w-full whitespace-nowrap'>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className='h-24 text-center'
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation='horizontal' />
        </ScrollArea>
      </div>
      <div className='flex items-center justify-end space-x-2 py-4'>
        <div className='text-muted-foreground flex-1 text-sm'>
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className='space-x-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant='outline'
            size='sm'
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}

const UserTableDtails = ({ u: data }: { u: applicationsData }) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const renderValue = (value: any) => {
    if (value === null) return 'N/A';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (Array.isArray(value)) return value.join(', ');
    return value;
  };

  if (data.join_role === 'Affiliate') {
    return (
      <div>
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <p className='font-semibold'>ID:</p>
            <p>{data.id}</p>
          </div>
          <div>
            <p className='font-semibold'>User ID:</p>
            <p>{data.user_id}</p>
          </div>
        </div>
        <div>
          <p className='font-semibold'>Full Name:</p>
          <p>{renderValue(data.full_name)}</p>
        </div>
        <div>
          <p className='font-semibold'>Email:</p>
          <p>{data.email}</p>
        </div>
        <div>
          <p className='font-semibold'>Phone:</p>
          <p>{renderValue(data.phone)}</p>
        </div>
        <div>
          <p className='font-semibold'>Date of Birth:</p>
          <p>{formatDate(data.dob)}</p>
        </div>
        <div>
          <p className='font-semibold'>Location:</p>
          <p>{renderValue(data.location)}</p>
        </div>
        <div>
          <p className='font-semibold'>Join Role:</p>
          <Badge variant='outline' className='mt-1'>
            {data.join_role}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Position:</p>
          <p>{renderValue(data.position)}</p>
        </div>
        <div>
          <p className='font-semibold'>Other Position:</p>
          <p>{renderValue(data.position_other)}</p>
        </div>
        <div>
          <p className='font-semibold'>Areas of Interest:</p>
          <p>{renderValue(data.areas)}</p>
        </div>
        <div>
          <p className='font-semibold'>Other Area:</p>
          <p>{renderValue(data.other_area)}</p>
        </div>
        <div>
          <p className='font-semibold'>Interests:</p>
          <p>{renderValue(data.interests)}</p>
        </div>
        <div>
          <p className='font-semibold'>Skills:</p>
          <p>{renderValue(data.skills)}</p>
        </div>
        <div>
          <p className='font-semibold'>Why Join:</p>
          <p>{renderValue(data.why_join)}</p>
        </div>
        <div>
          <p className='font-semibold'>Past Experience:</p>
          <p>{renderValue(data.past_experience)}</p>
        </div>
        <div>
          <p className='font-semibold'>Hours Per Week:</p>
          <p>{renderValue(data.hours_per_week)}</p>
        </div>
        <div>
          <p className='font-semibold'>Available Days:</p>
          <p>{renderValue(data.available_days)}</p>
        </div>
        <div>
          <p className='font-semibold'>Preferred Time:</p>
          <p>{renderValue(data.preferred_time)}</p>
        </div>
        <div>
          <p className='font-semibold'>Equipment:</p>
          <p>{renderValue(data.equipment)}</p>
        </div>
        <div>
          <p className='font-semibold'>Additional Info:</p>
          <p>{renderValue(data.additional_info)}</p>
        </div>
        <div>
          <p className='font-semibold'>Newsletter Subscription:</p>
          <p>{renderValue(data.newsletter)}</p>
        </div>
        <div>
          <p className='font-semibold'>Referer:</p>
          <p>{renderValue(data.referer)}</p>
        </div>
        <div>
          <p className='font-semibold'>Resume URL:</p>
          <p>
            {data.resume_url ? (
              <a
                href={data.resume_url}
                target='_blank'
                rel='noopener noreferrer'
                className='text-blue-500 hover:underline'
              >
                View Resume
              </a>
            ) : (
              'N/A'
            )}
          </p>
        </div>
        <div>
          <p className='font-semibold'>NDA Signed:</p>
          <p>{renderValue(data.is_nda_signed)}</p>
        </div>
        <div>
          <p className='font-semibold'>NDA Signed Date:</p>
          <p>{formatDate(data.nda_signed_date)}</p>
        </div>
        <div>
          <p className='font-semibold'>Volunteer Form Submitted:</p>
          <p>{renderValue(data.is_vol_form_submited)}</p>
        </div>
        <div>
          <p className='font-semibold'>Created At:</p>
          <p>{formatDate(data.created_at)}</p>
        </div>
        <div>
          <p className='font-semibold'>Approval Status:</p>
          <Badge
            variant={
              data.approved === 'accepted'
                ? 'default'
                : data.approved === 'notAccepted'
                  ? 'destructive'
                  : 'secondary'
            }
            className='mt-1'
          >
            {data.approved}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Review Status:</p>
          <Badge
            variant={data.reviewed === 'reviewed' ? 'default' : 'secondary'}
            className='mt-1'
          >
            {data.reviewed}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Message:</p>
          <p>{renderValue(data.message)}</p>
        </div>
      </div>
    );
  } else if (data.join_role === 'Collaborator') {
    return (
      <div>
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <p className='font-semibold'>ID:</p>
            <p>{data.id}</p>
          </div>
          <div>
            <p className='font-semibold'>User ID:</p>
            <p>{data.user_id}</p>
          </div>
        </div>
        <div>
          <p className='font-semibold'>Full Name:</p>
          <p>{renderValue(data.full_name)}</p>
        </div>
        <div>
          <p className='font-semibold'>Email:</p>
          <p>{data.email}</p>
        </div>
        <div>
          <p className='font-semibold'>Phone:</p>
          <p>{renderValue(data.phone)}</p>
        </div>
        <div>
          <p className='font-semibold'>Date of Birth:</p>
          <p>{formatDate(data.dob)}</p>
        </div>
        <div>
          <p className='font-semibold'>Location:</p>
          <p>{renderValue(data.location)}</p>
        </div>
        <div>
          <p className='font-semibold'>Join Role:</p>
          <Badge variant='outline' className='mt-1'>
            {data.join_role}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Position:</p>
          <p>{renderValue(data.position)}</p>
        </div>
        <div>
          <p className='font-semibold'>Other Position:</p>
          <p>{renderValue(data.position_other)}</p>
        </div>
        <div>
          <p className='font-semibold'>Areas of Interest:</p>
          <p>{renderValue(data.areas)}</p>
        </div>
        <div>
          <p className='font-semibold'>Other Area:</p>
          <p>{renderValue(data.other_area)}</p>
        </div>
        <div>
          <p className='font-semibold'>Interests:</p>
          <p>{renderValue(data.interests)}</p>
        </div>
        <div>
          <p className='font-semibold'>Skills:</p>
          <p>{renderValue(data.skills)}</p>
        </div>
        <div>
          <p className='font-semibold'>Why Join:</p>
          <p>{renderValue(data.why_join)}</p>
        </div>
        <div>
          <p className='font-semibold'>Past Experience:</p>
          <p>{renderValue(data.past_experience)}</p>
        </div>
        <div>
          <p className='font-semibold'>Hours Per Week:</p>
          <p>{renderValue(data.hours_per_week)}</p>
        </div>
        <div>
          <p className='font-semibold'>Available Days:</p>
          <p>{renderValue(data.available_days)}</p>
        </div>
        <div>
          <p className='font-semibold'>Preferred Time:</p>
          <p>{renderValue(data.preferred_time)}</p>
        </div>
        <div>
          <p className='font-semibold'>Equipment:</p>
          <p>{renderValue(data.equipment)}</p>
        </div>
        <div>
          <p className='font-semibold'>Additional Info:</p>
          <p>{renderValue(data.additional_info)}</p>
        </div>
        <div>
          <p className='font-semibold'>Newsletter Subscription:</p>
          <p>{renderValue(data.newsletter)}</p>
        </div>
        <div>
          <p className='font-semibold'>Referer:</p>
          <p>{renderValue(data.referer)}</p>
        </div>
        <div>
          <p className='font-semibold'>Resume URL:</p>
          <p>
            {data.resume_url ? (
              <a
                href={data.resume_url}
                target='_blank'
                rel='noopener noreferrer'
                className='text-blue-500 hover:underline'
              >
                View Resume
              </a>
            ) : (
              'N/A'
            )}
          </p>
        </div>
        <div>
          <p className='font-semibold'>NDA Signed:</p>
          <p>{renderValue(data.is_nda_signed)}</p>
        </div>
        <div>
          <p className='font-semibold'>NDA Signed Date:</p>
          <p>{formatDate(data.nda_signed_date)}</p>
        </div>
        <div>
          <p className='font-semibold'>Volunteer Form Submitted:</p>
          <p>{renderValue(data.is_vol_form_submited)}</p>
        </div>
        <div>
          <p className='font-semibold'>Created At:</p>
          <p>{formatDate(data.created_at)}</p>
        </div>
        <div>
          <p className='font-semibold'>Approval Status:</p>
          <Badge
            variant={
              data.approved === 'accepted'
                ? 'default'
                : data.approved === 'notAccepted'
                  ? 'destructive'
                  : 'secondary'
            }
            className='mt-1'
          >
            {data.approved}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Review Status:</p>
          <Badge
            variant={data.reviewed === 'reviewed' ? 'default' : 'secondary'}
            className='mt-1'
          >
            {data.reviewed}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Message:</p>
          <p>{renderValue(data.message)}</p>
        </div>
      </div>
    );
  } else if (data.join_role === 'Volunteer') {
    return (
      <div>
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <p className='font-semibold'>ID:</p>
            <p>{data.id}</p>
          </div>
          <div>
            <p className='font-semibold'>User ID:</p>
            <p>{data.user_id}</p>
          </div>
        </div>
        <div>
          <p className='font-semibold'>Full Name:</p>
          <p>{renderValue(data.full_name)}</p>
        </div>
        <div>
          <p className='font-semibold'>Email:</p>
          <p>{data.email}</p>
        </div>
        <div>
          <p className='font-semibold'>Phone:</p>
          <p>{renderValue(data.phone)}</p>
        </div>
        <div>
          <p className='font-semibold'>Date of Birth:</p>
          <p>{formatDate(data.dob)}</p>
        </div>
        <div>
          <p className='font-semibold'>Location:</p>
          <p>{renderValue(data.location)}</p>
        </div>
        <div>
          <p className='font-semibold'>Join Role:</p>
          <Badge variant='outline' className='mt-1'>
            {data.join_role}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Position:</p>
          <p>{renderValue(data.position)}</p>
        </div>
        <div>
          <p className='font-semibold'>Other Position:</p>
          <p>{renderValue(data.position_other)}</p>
        </div>
        <div>
          <p className='font-semibold'>Areas of Interest:</p>
          <p>{renderValue(data.areas)}</p>
        </div>
        <div>
          <p className='font-semibold'>Other Area:</p>
          <p>{renderValue(data.other_area)}</p>
        </div>
        <div>
          <p className='font-semibold'>Interests:</p>
          <p>{renderValue(data.interests)}</p>
        </div>
        <div>
          <p className='font-semibold'>Skills:</p>
          <p>{renderValue(data.skills)}</p>
        </div>
        <div>
          <p className='font-semibold'>Why Join:</p>
          <p>{renderValue(data.why_join)}</p>
        </div>
        <div>
          <p className='font-semibold'>Past Experience:</p>
          <p>{renderValue(data.past_experience)}</p>
        </div>
        <div>
          <p className='font-semibold'>Hours Per Week:</p>
          <p>{renderValue(data.hours_per_week)}</p>
        </div>
        <div>
          <p className='font-semibold'>Available Days:</p>
          <p>{renderValue(data.available_days)}</p>
        </div>
        <div>
          <p className='font-semibold'>Preferred Time:</p>
          <p>{renderValue(data.preferred_time)}</p>
        </div>
        <div>
          <p className='font-semibold'>Equipment:</p>
          <p>{renderValue(data.equipment)}</p>
        </div>
        <div>
          <p className='font-semibold'>Additional Info:</p>
          <p>{renderValue(data.additional_info)}</p>
        </div>
        <div>
          <p className='font-semibold'>Newsletter Subscription:</p>
          <p>{renderValue(data.newsletter)}</p>
        </div>
        <div>
          <p className='font-semibold'>Referer:</p>
          <p>{renderValue(data.referer)}</p>
        </div>
        <div>
          <p className='font-semibold'>Resume URL:</p>
          <p>
            {data.resume_url ? (
              <a
                href={data.resume_url}
                target='_blank'
                rel='noopener noreferrer'
                className='text-blue-500 hover:underline'
              >
                View Resume
              </a>
            ) : (
              'N/A'
            )}
          </p>
        </div>
        <div>
          <p className='font-semibold'>NDA Signed:</p>
          <p>{renderValue(data.is_nda_signed)}</p>
        </div>
        <div>
          <p className='font-semibold'>NDA Signed Date:</p>
          <p>{formatDate(data.nda_signed_date)}</p>
        </div>
        <div>
          <p className='font-semibold'>Volunteer Form Submitted:</p>
          <p>{renderValue(data.is_vol_form_submited)}</p>
        </div>
        <div>
          <p className='font-semibold'>Created At:</p>
          <p>{formatDate(data.created_at)}</p>
        </div>
        <div>
          <p className='font-semibold'>Approval Status:</p>
          <Badge
            variant={
              data?.approved! === 'accepted'
                ? 'default'
                : data?.approved! === 'notAccepted'
                  ? 'destructive'
                  : 'secondary'
            }
            className='mt-1'
          >
            {data.approved}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Review Status:</p>
          <Badge
            variant={data.reviewed === 'reviewed' ? 'default' : 'secondary'}
            className='mt-1'
          >
            {data.reviewed}
          </Badge>
        </div>
        <div>
          <p className='font-semibold'>Message:</p>
          <p>{renderValue(data.message)}</p>
        </div>
      </div>
    );
  } else null;
};
