'use client';

import { TrendingUp, TrendingDown } from 'lucide-react';
import { Area, AreaChart, CartesianGrid, XAxis, YA<PERSON>s, Legend } from 'recharts';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Database } from '@/lib/supabase/db/types';
import React from 'react';

type sp = Database['public']['Tables']['waitlists']['Row'];
type chartsData = {
  users: sp[];
};

export function WaitlistsCharts({ users }: chartsData) {
  const today = new Date();
  const weekAgo = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate() - 6
  );

  const salesData = users
    .filter((user) => new Date(user.created_at) >= weekAgo)
    .reduce(
      (acc, user) => {
        const date = new Date(user.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

  const chartData = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 6 + i
    );
    const dateString = date.toISOString().split('T')[0];
    return {
      day: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][date.getDay()],
      sales: salesData[dateString] || 0,
    };
  });

  const chartConfig = {
    sales: {
      label: 'Daily Sales',
      color: 'hsl(var(--chart-1))',
    },
  } satisfies ChartConfig;

  // Calculate trending percentage
  const lastTwoDays = chartData.slice(-2);
  const trendingPercentage =
    lastTwoDays.length === 2 && lastTwoDays[0].sales !== 0
      ? ((lastTwoDays[1].sales - lastTwoDays[0].sales) / lastTwoDays[0].sales) *
        100
      : 0;

  return (
    <div className='flex flex-col overflow-hidden items-start  md:items-start p-4 py-10'>
      <div className='mb-4'>
        <p className='text-xl font-bold'>Daily Waitlist Joins</p>
        <p className='text-sm text-gray-600'>
          Showing total Waitlists for the last week
        </p>
      </div>
      <div className='mb-4'>
        <ChartContainer
          config={chartConfig}
          className='w-[80dvw] md:w-full h-auto   max-w-screen-sm md:h-[400px]'
        >
          <AreaChart
            accessibilityLayer
            data={chartData}
            // margin={{
            //   left: 20,
            //   right: 20,
            //   top: 30,
            //   bottom: 30,
            // }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey='day'
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              label={{
                value: 'Number of Users Joined',
                angle: -90,
                position: 'insideLeft',
                style: { textAnchor: 'middle' },
                offset: 0,
              }}
            />
            <ChartTooltip
              cursor={false}
              content={({ active, payload }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className='bg-white p-2 border rounded shadow'>
                      <p className='text-sm font-medium'>
                        {payload[0].payload.day}
                      </p>
                      <p className='text-sm'>
                        <span className='font-medium text-neutral-500'>
                          Joined Users:
                        </span>{' '}
                        {payload[0].value}
                      </p>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Area
              dataKey='sales'
              type='monotone'
              fill='var(--color-sales)'
              fillOpacity={0.4}
              stroke='var(--color-sales)'
            />
            <Legend />
          </AreaChart>
        </ChartContainer>
      </div>
      <div className='mt-4'>
        <div className='flex w-full items-start gap-2 text-sm'>
          <div className='grid gap-2'>
            <div className='flex items-center gap-2 font-medium leading-none'>
              {trendingPercentage > 0 ? (
                <>
                  Trending up by {trendingPercentage.toFixed(1)}% today{' '}
                  <TrendingUp className='h-4 w-4 text-green-500' />
                </>
              ) : trendingPercentage < 0 ? (
                <>
                  Trending down by {Math.abs(trendingPercentage).toFixed(1)}%
                  today <TrendingDown className='h-4 w-4 text-red-500' />
                </>
              ) : (
                <>
                  No change in trend today <span className='h-4 w-4'>-</span>
                </>
              )}
            </div>
            <div className='flex items-center gap-2 leading-none text-gray-600'>
              {weekAgo.toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'short',
                year: 'numeric',
              })}
              -
              {today.toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'short',
                year: 'numeric',
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
