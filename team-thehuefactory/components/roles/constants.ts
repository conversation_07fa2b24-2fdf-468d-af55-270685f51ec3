import { useGetUser } from '@/lib/supabase/auth/getUser';
import { Database } from '@/lib/supabase/db/types';

type r = Database['public']['Enums']['Role Types'];

const ADMIN_ROLE_LINKS = [
  { title: 'Dashboard', url: '/dashboard' },
  { title: 'Applications', url: '/dashboard/applications' },
  { title: 'Waitlists', url: '/dashboard/waitlist' },
  { title: 'Emails', url: '/dashboard/emails' },
  { title: 'Proposals', url: '/dashboard/proposals' },
];
const COLLABORATOR_ROLE_LINKS = [
  { title: 'Dashboard', url: '/dashboard' },
  { title: 'NDA', url: '/dashboard/nda' },
];
const VOLUNTEER_ROLE_LINKS = [
  { title: 'Dashboard', url: '/dashboard' },
  { title: 'NDA', url: '/dashboard/nda' },
];
const AFFILIATE_ROLE_LINKS = [
  { title: 'Dashboard', url: '/dashboard' },
  { title: 'Referals', url: '/dashboard/referals' },
  { title: 'NDA', url: '/dashboard/nda' },
];

export function NavLinks(role: r) {
  if (role === 'Admin') {
    return ADMIN_ROLE_LINKS;
  } else if (role === 'Affiliate') {
    return AFFILIATE_ROLE_LINKS;
  } else if (role === 'Collaborator') {
    return COLLABORATOR_ROLE_LINKS;
  } else if (role === 'Volunteer') {
    return VOLUNTEER_ROLE_LINKS;
  } else {
    return null;
  }
}
