import { User } from '@supabase/supabase-js';
import { create } from 'zustand';
import { Database } from '../supabase/db/types';

type user = User | null;
type profile = Database['public']['Tables']['profiles']['Row'] | null;
type r = Database['public']['Enums']['Role Types'];

type State = {
  user: user;
  profile: profile;
  role: r | null;
};

type Action = {
  updateUser: (user: State['user']) => void;
  removeUser: (user: State['user']) => void;
  updateRole: (user: State['role']) => void;
  removeRole: (user: State['role']) => void;
  updateProfile: (profile: State['profile']) => void;
  removeProfile: (profile: State['profile']) => void;
};

export const userStore = create<State & Action>((set) => ({
  user: null,
  profile: null,
  role: null,
  updateUser: (user) => set(() => ({ user: user })),
  removeUser: () => set(() => ({ user: null })),
  updateRole: (role) => set(() => ({ role: role })),
  removeRole: () => set(() => ({ role: null })),
  updateProfile: (profile) => set(() => ({ profile: profile })),
  removeProfile: () => set(() => ({ profile: null })),
}));
