import { useEffect } from 'react';
import { supabaseClient } from './client';
import { userStore } from '@/lib/store/user';
const supabase = supabaseClient();

export function useGetUser() {
  const {
    user,
    profile,
    role,
    updateRole,
    removeRole,
    updateUser,
    removeUser,
    updateProfile,
    removeProfile,
  } = userStore((state) => state);

  useEffect(() => {
    supabase.auth.getUser().then(({ data: { user } }) => {
      updateUser(user!);
      if (user!) {
        supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()
          .then(({ data, error }) => {
            if (error || !data) {
              console.log(error);
            } else {
              updateProfile(data);
              updateRole(data.role);
            }
          });
      }
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      if (!session) {
        removeUser(null);
        removeProfile(null);
        removeRole(null);
      }
      updateUser(session?.user!);
      if (session?.user!) {
        supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single()
          .then(({ data, error }) => {
            if (error || !data) {
              console.log(error);
            } else {
              updateProfile(data);
              updateRole(data.role);
            }
          });
      }
    });
  }, []);

  return { user, profile, role };
}
