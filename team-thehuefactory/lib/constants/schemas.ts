import { z } from 'zod';

// TODO: Refactor some of the validators. Ex: name and zipCode or address and country have same rules
// Field Validators
const fieldValidators = {
  name: z
    .string()
    .min(2, { message: 'Must be at least 2 characters' })
    .max(50, { message: 'Must be at most 50 characters' }),
  address: z
    .string()
    .min(2, { message: 'Must be at least 2 characters' })
    .max(70, { message: 'Must be between 2 and 70 characters' }),
  zipCode: z
    .string()
    .min(2, { message: 'Must be between 2 and 20 characters' })
    .max(20, { message: 'Must be between 2 and 20 characters' }),
  city: z
    .string()
    .min(1, { message: 'Must be between 1 and 50 characters' })
    .max(50, { message: 'Must be between 1 and 50 characters' }),
  country: z
    .string()
    .min(1, { message: 'Must be between 1 and 70 characters' })
    .max(70, { message: 'Must be between 1 and 70 characters' }),
  email: z
    .string()
    .email({ message: 'Email must be a valid email' })
    .min(5, { message: 'Must be between 5 and 30 characters' })
    .max(30, { message: 'Must be between 5 and 30 characters' }),
  phone: z
    .string()
    .min(1, { message: 'Must be between 1 and 50 characters' })
    .max(50, {
      message: 'Must be between 1 and 50 characters',
    }),

  // Strings
  string: z.string(),
  stringMin1: z.string().min(1, { message: 'Must be at least 1 character' }),
  stringToNumber: z.coerce.number(),

  // Charges
  stringToNumberWithMax: z.coerce.number().max(1000000),

  stringOptional: z.string().optional(),

  nonNegativeNumber: z.coerce.number().nonnegative({
    message: 'Must be a positive number',
  }),
};

// Auth An Onboarding Scemmas
const OnboardingSchema = z.object({
  full_name: fieldValidators.name,
  email: fieldValidators.email,
  phone_number: fieldValidators.phone,
});

export { OnboardingSchema };
