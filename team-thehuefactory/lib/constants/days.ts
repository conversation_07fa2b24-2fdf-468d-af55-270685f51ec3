import { format, addDays, isWeekend } from 'date-fns';

// Function to get the next working day
const getNextWorkingDay = (date: Date): Date => {
  let nextDay = addDays(date, 1);
  while (isWeekend(nextDay)) {
    nextDay = addDays(nextDay, 1);
  }
  return nextDay;
};

const cD = new Date();

// Get the next working day in the following week
const nWWD = addDays(getNextWorkingDay(cD), 7);

// Format the date
const formattedNWWD = format(nWWD, 'MMMM d, yyyy');
const formattedDate = format(cD, 'MMMM d, yyyy');

export { formattedNWWD as nextWeekWorkingDay, formattedDate as currentDate };
