import { Database } from '@/lib/supabase/db/types';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import { z } from 'zod';

const baseUrl = 'https://www.thehuefactory.co/';

type dataProps = {
  approved: Database['public']['Enums']['is_accepted'];
  created_at: string;
  email: string | null;
  full_name: string | null;
  id: number;
  join_role: Database['public']['Enums']['Role Types'];
  location: string | null;
  message: string | null;
  phone: string | null;
  position: string | null;
  position_other: string | null;
  referer: string | null;
  resume_url: string | null;
  reviewed: Database['public']['Enums']['is_reviewed'];
  user_id: string;
  pass: string;
};

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const AcceptedApplication = ({
  full_name,
  join_role,
  email,
  pass,
}: dataProps) => (
  <Html>
    <Head />
    <Body style={main}>
      <Container
        style={{
          ...container,
          backgroundColor: colors['100'],
        }}
      >
        <Img
          src={`${baseUrl}/thehuefactory_hero.png`}
          width='100%'
          height='auto'
          alt='Email Header Image'
        />
      </Container>
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['100'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        ></Section>
      </Container>
      <Container style={container}>
        <Heading style={h1}>Hey {full_name}, </Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          We are thrilled to inform you that your Application as a {join_role}{' '}
          has been approved!
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Here are the details to get you started: <br />
          Email:<code style={codeold}>{email}</code> <br />
          Password: <code style={codeold}>{pass}</code>
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Please visit the following URL to log in to your dashboard and accept
          the terms and conditions:
        </Text>
        <Link
          href='https://team.thehuefactory.co/dashboard'
          target='_blank'
          style={{
            backgroundColor: colors[100],
            paddingRight: 18,
            paddingLeft: 18,
            fontWeight: 'bold',
            color: 'white',
            paddingTop: 16,
            paddingBottom: 16,
            borderRadius: 32,
            whiteSpace: 'nowrap',
            fontFamily: 'monospace',
          }}
        >
          View {join_role} Dashboard
        </Link>
        <Text style={{ ...text, marginBottom: '24px' }}>
          You Can Reply This messgae with any extra qustions or{' '}
          <Link
            href='https://www.thehuefactory.co/contact'
            target='_blank'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
          >
            contact us
          </Link>{' '}
          with your questions.
        </Text>
      </Container>
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
          height='42'
          alt="thehuefactory's Logo"
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            target='_blank'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2024 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default AcceptedApplication;

const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const codeold = {
  display: 'inline-block',
  padding: '16px 4.5%',
  width: '90.5%',
  backgroundColor: '#f4f4f4',
  borderRadius: '1px',
  border: '1px solid #eee',
  color: '#333',
};

const code = {
  fontFamily: 'monospace',
  fontWeight: '400',
  padding: '16px 4.5%',
  backgroundColor: '#dfe1e4',
  letterSpacing: '-0.3px',
  fontSize: '16px',
  borderRadius: '1px',
  color: '#3c4149',
};
