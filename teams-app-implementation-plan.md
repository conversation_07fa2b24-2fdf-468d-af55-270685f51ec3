# Teams Application Implementation Plan

## Executive Summary

This document provides a comprehensive implementation plan for creating a new `apps/teams` application that combines the complete team management functionality from `team-thehuefactory/` with the sophisticated Linear-inspired UI/UX design system from `circle-temp/`. The implementation follows a phased approach, leveraging the existing monorepo structure and creating reusable packages for global use.

### Project Objectives
1. **Functional Completeness**: Preserve all team management features (authentication, roles, applications, NDAs, proposals, etc.)
2. **Visual Excellence**: Implement Circle-temp's sophisticated OKLCH color system, custom icons, and animations
3. **Global Reusability**: Create packages that can be used across the entire monorepo
4. **Performance**: Optimize for speed and responsiveness with modern patterns
5. **Maintainability**: Establish clear architecture and documentation

### Key Success Metrics
- ✅ Zero functionality loss from existing team system
- ✅ 100% visual design preservation from Circle-temp
- ✅ <2s initial page load time
- ✅ Mobile-first responsive design
- ✅ Accessibility compliance (WCAG 2.1 AA)

## Architecture Overview

### Monorepo Structure
```
thehuefactory/
├── apps/
│   ├── web/                    # Main website
│   └── teams/                  # New teams application
├── packages/
│   ├── ui/                     # Enhanced UI components
│   ├── teams-ui/               # Teams-specific components
│   ├── teams-auth/             # Team authentication
│   ├── teams-db/               # Team database utilities
│   ├── teams-emails/           # Team email templates
│   ├── icons/                  # Custom icon system
│   ├── animations/             # Motion components
│   └── theme/                  # OKLCH theme system
```

### Package Dependencies
```mermaid
graph TD
    A[apps/teams] --> B[@thf/teams-ui]
    A --> C[@thf/teams-auth]
    A --> D[@thf/teams-db]
    A --> E[@thf/teams-emails]
    
    B --> F[@thf/ui]
    B --> G[@thf/icons]
    B --> H[@thf/animations]
    B --> I[@thf/theme]
    
    C --> J[@thf/supabase]
    D --> J
    E --> K[@thf/emails]
    
    F --> I
    G --> I
    H --> L[motion]
```

## Phase 1: Foundation & Package Architecture (Weeks 1-2)

### 1.1 Package Structure Setup

#### Create New Packages
```bash
# Core theme and design system
mkdir -p packages/theme/src
mkdir -p packages/icons/src/{status,priority,navigation}
mkdir -p packages/animations/src/{layout,micro,transitions}

# Teams-specific packages
mkdir -p packages/teams-ui/src/{components,layouts,forms}
mkdir -p packages/teams-auth/src/{hooks,providers,middleware}
mkdir -p packages/teams-db/src/{types,queries,mutations}
mkdir -p packages/teams-emails/src/{templates,components}
```

#### Package Configuration Files
```json
// packages/theme/package.json
{
  "name": "@thf/theme",
  "version": "0.0.0",
  "type": "module",
  "private": true,
  "exports": {
    "./colors": "./src/colors.ts",
    "./fonts": "./src/fonts.ts",
    "./theme-provider": "./src/theme-provider.tsx"
  },
  "dependencies": {
    "next-themes": "^0.4.4"
  }
}
```

### 1.2 OKLCH Color System Implementation

#### Theme Package Structure
```typescript
// packages/theme/src/colors.ts
export const oklchColors = {
  light: {
    background: 'oklch(1 0 0)',
    foreground: 'oklch(0.141 0.005 285.823)',
    primary: 'oklch(0.21 0.006 285.885)',
    // ... complete color system
  },
  dark: {
    background: 'oklch(0.141 0.005 285.823)',
    foreground: 'oklch(0.985 0 0)',
    primary: 'oklch(0.985 0 0)',
    // ... complete color system
  }
};

// packages/theme/src/theme-provider.tsx
export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <NextThemesProvider attribute="class" defaultTheme="dark" enableSystem>
      {children}
    </NextThemesProvider>
  );
};
```

### 1.3 Custom Icon System

#### Icon Package Structure
```typescript
// packages/icons/src/status/index.tsx
export const StatusIcons = {
  backlog: BacklogIcon,
  todo: ToDoIcon,
  inProgress: InProgressIcon,
  technicalReview: TechnicalReviewIcon,
  completed: CompletedIcon,
  paused: PausedIcon,
};

// packages/icons/src/priority/index.tsx
export const PriorityIcons = {
  noPriority: NoPriorityIcon,
  low: LowPriorityIcon,
  medium: MediumPriorityIcon,
  high: HighPriorityIcon,
  urgent: UrgentPriorityIcon,
};
```

### 1.4 Animation System

#### Animation Package Structure
```typescript
// packages/animations/src/layout/index.tsx
export const LayoutAnimations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  slideUp: {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 },
  },
};

// packages/animations/src/micro/badge-stack.tsx
export const BadgeStack = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    className="-space-x-5 hover:space-x-1 transition-all duration-200"
    whileHover={{ scale: 1.02 }}
  >
    {children}
  </motion.div>
);
```

## Phase 2: Core UI Components Migration (Weeks 3-4)

### 2.1 Enhanced UI Components

#### Button System Enhancement
```typescript
// packages/ui/src/components/button.tsx (Enhanced)
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90',
        outline: 'border border-input bg-background shadow-xs hover:bg-accent',
        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2',
        xxs: 'h-6 rounded-md gap-1.5 px-2.5',
        xs: 'h-7 rounded-md gap-1.5 px-2.5',
        sm: 'h-8 rounded-md gap-1.5 px-3',
        lg: 'h-10 rounded-md px-6',
        icon: 'size-9',
      },
    },
  }
);
```

#### Sidebar Component System
```typescript
// packages/teams-ui/src/layouts/main-layout.tsx
interface MainLayoutProps {
  children: React.ReactNode;
  header: React.ReactNode;
  headersNumber?: 1 | 2;
}

export const MainLayout = ({ children, header, headersNumber = 2 }: MainLayoutProps) => {
  const height = {
    1: 'h-[calc(100svh-40px)] lg:h-[calc(100svh-56px)]',
    2: 'h-[calc(100svh-80px)] lg:h-[calc(100svh-96px)]',
  };

  return (
    <SidebarProvider>
      <TeamsSidebar />
      <div className="h-svh overflow-hidden lg:p-2 w-full">
        <div className="lg:border lg:rounded-md overflow-hidden flex flex-col bg-container h-full w-full">
          {header}
          <div className={cn('overflow-auto w-full', height[headersNumber])}>
            {children}
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
};
```

### 2.2 Teams-Specific Components

#### Application Line Component
```typescript
// packages/teams-ui/src/components/application-line.tsx
interface ApplicationLineProps {
  application: TeamApplication;
  layoutId?: boolean;
}

export const ApplicationLine = ({ application, layoutId = false }: ApplicationLineProps) => (
  <ContextMenu>
    <ContextMenuTrigger asChild>
      <motion.div
        {...(layoutId && { layoutId: `application-line-${application.id}` })}
        className="w-full flex items-center justify-start h-11 px-6 hover:bg-sidebar/50"
      >
        <div className="flex items-center gap-0.5">
          <StatusSelector status={application.status} applicationId={application.id} />
          <span className="text-sm text-muted-foreground font-medium w-[66px] truncate">
            {application.identifier}
          </span>
        </div>
        <span className="min-w-0 flex items-center justify-start mr-1 ml-0.5">
          <span className="text-xs sm:text-sm font-medium sm:font-semibold truncate">
            {application.fullName}
          </span>
        </span>
        <div className="flex items-center justify-end gap-2 ml-auto">
          <RoleBadge role={application.role} />
          <span className="text-xs text-muted-foreground">
            {format(new Date(application.createdAt), 'MMM dd')}
          </span>
          <UserAvatar user={application.user} />
        </div>
      </motion.div>
    </ContextMenuTrigger>
    <ApplicationContextMenu applicationId={application.id} />
  </ContextMenu>
);
```

## Phase 3: Authentication & Database Integration (Weeks 5-6)

### 3.1 Teams Authentication Package

#### Auth Provider Setup
```typescript
// packages/teams-auth/src/providers/auth-provider.tsx
export const TeamsAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [role, setRole] = useState<TeamRole | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          const profile = await getProfile(session.user.id);
          setUser(session.user);
          setRole(profile?.role || null);
        } else {
          setUser(null);
          setRole(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return (
    <TeamsAuthContext.Provider value={{ user, role, loading }}>
      {children}
    </TeamsAuthContext.Provider>
  );
};
```

### 3.2 Database Integration

#### Teams Database Package
```typescript
// packages/teams-db/src/types/index.ts
export interface TeamApplication {
  id: string;
  identifier: string;
  fullName: string;
  email: string;
  role: TeamRole;
  status: ApplicationStatus;
  createdAt: string;
  isNdaSigned: boolean | null;
  ndaSignedDate: string | null;
  // ... other fields
}

export interface ApplicationStatus {
  id: string;
  name: string;
  color: string;
  icon: React.FC;
}

// packages/teams-db/src/queries/applications.ts
export const useApplications = () => {
  return useQuery({
    queryKey: ['applications'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('JOIN_US_TABLE')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });
};
```

## Phase 4: Email System Integration (Weeks 7-8)

### 4.1 Teams Email Package

#### Email Templates with React Email
```typescript
// packages/teams-emails/src/templates/application-approved.tsx
export const ApplicationApprovedEmail = ({ application }: { application: TeamApplication }) => (
  <Html>
    <Head />
    <Preview>Your application has been approved!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Img src="/logo.png" width="40" height="40" alt="The Hue Factory" />
        <Text style={h1}>Welcome to The Hue Factory!</Text>
        <Text style={text}>
          Hi {application.fullName}, your application for the {application.role} role has been approved.
        </Text>
        <Button style={button} href={`${process.env.TEAMS_URL}/dashboard`}>
          Access Dashboard
        </Button>
      </Container>
    </Body>
  </Html>
);
```

## Deliverables Summary

### Phase 1 Deliverables
- [ ] Complete package architecture setup
- [ ] OKLCH color system implementation
- [ ] Custom icon system (status, priority, navigation)
- [ ] Animation system foundation
- [ ] Theme provider with dark-first approach

### Phase 2 Deliverables
- [ ] Enhanced UI component library
- [ ] Teams-specific component system
- [ ] Responsive layout components
- [ ] Application management interface
- [ ] Sidebar navigation system

### Phase 3 Deliverables
- [ ] Teams authentication package
- [ ] Database integration with real-time updates
- [ ] Role-based access control
- [ ] Application workflow management
- [ ] NDA management system

### Phase 4 Deliverables
- [ ] Email template system
- [ ] Automated notification workflows
- [ ] Email tracking and analytics
- [ ] Multi-role email templates
- [ ] Integration testing

## Phase 5: Teams Application Development (Weeks 9-12)

### 5.1 Teams App Structure

#### Application Directory Structure
```
apps/teams/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   │   └── page.tsx
│   │   └── layout.tsx
│   ├── (dashboard)/
│   │   ├── applications/
│   │   │   └── page.tsx
│   │   ├── members/
│   │   │   └── page.tsx
│   │   ├── projects/
│   │   │   └── page.tsx
│   │   ├── proposals/
│   │   │   └── page.tsx
│   │   ├── nda/
│   │   │   └── page.tsx
│   │   ├── settings/
│   │   │   └── page.tsx
│   │   ├── page.tsx
│   │   └── layout.tsx
│   ├── api/
│   │   ├── applications/
│   │   │   ├── approve/
│   │   │   ├── reject/
│   │   │   └── route.ts
│   │   ├── emails/
│   │   │   └── route.ts
│   │   └── nda/
│   │       └── route.ts
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── dashboard/
│   ├── forms/
│   └── layouts/
├── lib/
│   ├── auth.ts
│   ├── db.ts
│   └── utils.ts
├── middleware.ts
├── next.config.ts
├── package.json
├── tailwind.config.ts
└── tsconfig.json
```

#### Package.json Configuration
```json
{
  "name": "teams",
  "version": "1.0.0",
  "type": "module",
  "private": true,
  "scripts": {
    "dev": "next dev --port 3001 --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint --max-warnings 0",
    "check-types": "tsc --noEmit"
  },
  "dependencies": {
    "@thf/ui": "*",
    "@thf/teams-ui": "*",
    "@thf/teams-auth": "*",
    "@thf/teams-db": "*",
    "@thf/teams-emails": "*",
    "@thf/theme": "*",
    "@thf/icons": "*",
    "@thf/animations": "*",
    "@thf/supabase": "*",
    "@thf/store": "*",
    "@thf/constants": "*",
    "next": "^15.3.0",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "motion": "^12.4.10",
    "@tanstack/react-query": "^5.0.0",
    "react-hook-form": "^7.54.2",
    "zod": "^3.24.2"
  }
}
```

### 5.2 Root Layout Implementation

#### Main Layout with Theme Integration
```typescript
// apps/teams/app/layout.tsx
import { Geist, Geist_Mono } from 'next/font/google';
import { ThemeProvider } from '@thf/theme/theme-provider';
import { TeamsAuthProvider } from '@thf/teams-auth/providers/auth-provider';
import { QueryProvider } from '@thf/teams-ui/providers/query-provider';
import { Toaster } from '@thf/ui/components/sonner';
import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background`}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
          <QueryProvider>
            <TeamsAuthProvider>
              {children}
              <Toaster />
            </TeamsAuthProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

#### Global Styles with OKLCH
```css
/* apps/teams/app/globals.css */
@import '@thf/theme/colors';
@import 'tailwindcss';
@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* OKLCH Color Variables */
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.871 0.006 286.286);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.871 0.006 286.286);
  --container: #fff;
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.141 0.005 285.823);
  --card-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.274 0.006 286.033);
  --input: oklch(0.274 0.006 286.033);
  --ring: oklch(0.442 0.017 285.786);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.274 0.006 286.033);
  --sidebar-ring: oklch(0.442 0.017 285.786);
  --container: #101011;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}
```

### 5.3 Dashboard Implementation

#### Main Dashboard Page
```typescript
// apps/teams/app/(dashboard)/page.tsx
import { MainLayout } from '@thf/teams-ui/layouts/main-layout';
import { DashboardHeader } from '@thf/teams-ui/components/headers/dashboard-header';
import { ApplicationsOverview } from '@thf/teams-ui/components/dashboard/applications-overview';
import { TeamMetrics } from '@thf/teams-ui/components/dashboard/team-metrics';
import { RecentActivity } from '@thf/teams-ui/components/dashboard/recent-activity';
import { useTeamsAuth } from '@thf/teams-auth/hooks/use-teams-auth';
import { redirect } from 'next/navigation';

export default function DashboardPage() {
  const { user, role, loading } = useTeamsAuth();

  if (loading) {
    return <DashboardSkeleton />;
  }

  if (!user) {
    redirect('/login');
  }

  return (
    <MainLayout header={<DashboardHeader />}>
      <div className="p-6 space-y-6">
        {role === 'Admin' && (
          <>
            <TeamMetrics />
            <ApplicationsOverview />
          </>
        )}
        <RecentActivity />
      </div>
    </MainLayout>
  );
}
```

#### Applications Management Page
```typescript
// apps/teams/app/(dashboard)/applications/page.tsx
import { MainLayout } from '@thf/teams-ui/layouts/main-layout';
import { ApplicationsHeader } from '@thf/teams-ui/components/headers/applications-header';
import { ApplicationsList } from '@thf/teams-ui/components/applications/applications-list';
import { ApplicationsFilters } from '@thf/teams-ui/components/applications/applications-filters';
import { useApplications } from '@thf/teams-db/queries/applications';

export default function ApplicationsPage() {
  const { data: applications, isLoading } = useApplications();

  return (
    <MainLayout header={<ApplicationsHeader />}>
      <div className="flex flex-col h-full">
        <ApplicationsFilters />
        <ApplicationsList applications={applications} loading={isLoading} />
      </div>
    </MainLayout>
  );
}
```

## Phase 6: Advanced Features & Polish (Weeks 13-16)

### 6.1 Drag & Drop Implementation

#### Application Status Management
```typescript
// packages/teams-ui/src/components/applications/applications-grid.tsx
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { motion, AnimatePresence } from 'motion/react';

export const ApplicationsGrid = ({ applications }: { applications: TeamApplication[] }) => {
  const groupedApplications = groupBy(applications, 'status.id');

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 p-6">
        {Object.entries(groupedApplications).map(([statusId, apps]) => (
          <StatusColumn key={statusId} statusId={statusId} applications={apps} />
        ))}
      </div>
    </DndProvider>
  );
};

const StatusColumn = ({ statusId, applications }: StatusColumnProps) => {
  const [{ isOver }, drop] = useDrop({
    accept: 'application',
    drop: (item: { applicationId: string }) => {
      updateApplicationStatus(item.applicationId, statusId);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  return (
    <div
      ref={drop}
      className={cn(
        'bg-card rounded-lg p-4 min-h-[400px]',
        isOver && 'bg-accent/50 border-2 border-dashed border-primary'
      )}
    >
      <h3 className="font-semibold mb-4">{getStatusName(statusId)}</h3>
      <AnimatePresence>
        {applications.map((application) => (
          <motion.div
            key={application.id}
            layout
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-3"
          >
            <DraggableApplicationCard application={application} />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};
```

### 6.2 Real-time Updates

#### Supabase Real-time Integration
```typescript
// packages/teams-db/src/hooks/use-realtime-applications.ts
export const useRealtimeApplications = () => {
  const [applications, setApplications] = useState<TeamApplication[]>([]);

  useEffect(() => {
    const channel = supabase
      .channel('applications-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'JOIN_US_TABLE',
        },
        (payload) => {
          setApplications((current) => {
            switch (payload.eventType) {
              case 'INSERT':
                return [...current, payload.new as TeamApplication];
              case 'UPDATE':
                return current.map((app) =>
                  app.id === payload.new.id ? (payload.new as TeamApplication) : app
                );
              case 'DELETE':
                return current.filter((app) => app.id !== payload.old.id);
              default:
                return current;
            }
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return applications;
};
```

### 6.3 Advanced Search & Filtering

#### Global Search Implementation
```typescript
// packages/teams-ui/src/components/search/global-search.tsx
export const GlobalSearch = () => {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState('');
  const { data: searchResults } = useSearch(query);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <CommandInput
        placeholder="Search applications, members, projects..."
        value={query}
        onValueChange={setQuery}
      />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup heading="Applications">
          {searchResults?.applications?.map((app) => (
            <CommandItem key={app.id} onSelect={() => navigateToApplication(app.id)}>
              <UserIcon className="mr-2 h-4 w-4" />
              {app.fullName} - {app.role}
            </CommandItem>
          ))}
        </CommandGroup>
        <CommandGroup heading="Members">
          {searchResults?.members?.map((member) => (
            <CommandItem key={member.id} onSelect={() => navigateToMember(member.id)}>
              <UsersIcon className="mr-2 h-4 w-4" />
              {member.name}
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
};
```

## Implementation Timeline & Dependencies

### Timeline Overview (16 Weeks Total)

| Phase | Duration | Key Deliverables | Dependencies |
|-------|----------|------------------|--------------|
| **Phase 1** | Weeks 1-2 | Package architecture, OKLCH colors, icons | None |
| **Phase 2** | Weeks 3-4 | Enhanced UI components, layouts | Phase 1 complete |
| **Phase 3** | Weeks 5-6 | Authentication, database integration | Phase 2 complete |
| **Phase 4** | Weeks 7-8 | Email system, notifications | Phase 3 complete |
| **Phase 5** | Weeks 9-12 | Teams app development | Phases 1-4 complete |
| **Phase 6** | Weeks 13-16 | Advanced features, polish | Phase 5 complete |

### Critical Path Dependencies

#### Week-by-Week Breakdown
```mermaid
gantt
    title Teams App Implementation Timeline
    dateFormat  YYYY-MM-DD
    section Phase 1: Foundation
    Package Setup           :p1-1, 2024-01-01, 7d
    OKLCH Colors           :p1-2, after p1-1, 7d

    section Phase 2: UI Components
    Enhanced Components    :p2-1, after p1-2, 7d
    Teams Components      :p2-2, after p2-1, 7d

    section Phase 3: Backend
    Authentication        :p3-1, after p2-2, 7d
    Database Integration  :p3-2, after p3-1, 7d

    section Phase 4: Email
    Email Templates       :p4-1, after p3-2, 7d
    Notification System   :p4-2, after p4-1, 7d

    section Phase 5: Teams App
    App Structure         :p5-1, after p4-2, 7d
    Dashboard Pages       :p5-2, after p5-1, 7d
    Application Management :p5-3, after p5-2, 7d
    API Routes            :p5-4, after p5-3, 7d

    section Phase 6: Advanced
    Drag & Drop           :p6-1, after p5-4, 7d
    Real-time Updates     :p6-2, after p6-1, 7d
    Search & Filters      :p6-3, after p6-2, 7d
    Testing & Polish      :p6-4, after p6-3, 7d
```

### Resource Allocation

#### Team Structure
- **Lead Developer** (1): Architecture, complex components, integration
- **UI/UX Developer** (1): Design system, animations, responsive design
- **Backend Developer** (1): Database, authentication, API routes
- **QA Engineer** (0.5): Testing, accessibility, performance

#### Skill Requirements
- **React/Next.js**: Advanced (App Router, Server Components)
- **TypeScript**: Advanced (Complex types, generics)
- **Tailwind CSS**: Advanced (Custom configurations, plugins)
- **Framer Motion**: Intermediate (Layout animations, gestures)
- **Supabase**: Intermediate (Real-time, RLS, auth)
- **Testing**: Intermediate (Jest, Testing Library, Playwright)

## Testing Strategy

### 6.4 Testing Implementation

#### Unit Testing Setup
```typescript
// packages/teams-ui/src/components/__tests__/application-line.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ApplicationLine } from '../application-line';
import { mockApplication } from '../../__mocks__/applications';

describe('ApplicationLine', () => {
  it('renders application information correctly', () => {
    render(<ApplicationLine application={mockApplication} />);

    expect(screen.getByText(mockApplication.fullName)).toBeInTheDocument();
    expect(screen.getByText(mockApplication.identifier)).toBeInTheDocument();
    expect(screen.getByRole('img', { name: /status/i })).toBeInTheDocument();
  });

  it('handles status change on drag and drop', async () => {
    const onStatusChange = jest.fn();
    render(
      <ApplicationLine
        application={mockApplication}
        onStatusChange={onStatusChange}
      />
    );

    // Simulate drag and drop
    const element = screen.getByTestId('application-line');
    fireEvent.dragStart(element);
    fireEvent.drop(element);

    expect(onStatusChange).toHaveBeenCalledWith(mockApplication.id, 'reviewed');
  });
});
```

#### Integration Testing
```typescript
// apps/teams/__tests__/integration/applications.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { ApplicationsPage } from '../app/(dashboard)/applications/page';
import { TestProviders } from '../__tests__/utils/test-providers';

describe('Applications Page Integration', () => {
  it('loads and displays applications', async () => {
    render(
      <TestProviders>
        <ApplicationsPage />
      </TestProviders>
    );

    await waitFor(() => {
      expect(screen.getByText('Applications')).toBeInTheDocument();
    });

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Collaborator')).toBeInTheDocument();
  });

  it('filters applications by status', async () => {
    render(
      <TestProviders>
        <ApplicationsPage />
      </TestProviders>
    );

    const statusFilter = screen.getByLabelText('Filter by status');
    fireEvent.change(statusFilter, { target: { value: 'reviewed' } });

    await waitFor(() => {
      expect(screen.queryByText('Pending Application')).not.toBeInTheDocument();
      expect(screen.getByText('Reviewed Application')).toBeInTheDocument();
    });
  });
});
```

#### E2E Testing with Playwright
```typescript
// apps/teams/e2e/applications.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Applications Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/dashboard');
  });

  test('admin can approve application', async ({ page }) => {
    await page.goto('/applications');

    // Find pending application
    const applicationRow = page.locator('[data-testid="application-row"]').first();
    await expect(applicationRow).toContainText('Pending');

    // Open context menu and approve
    await applicationRow.click({ button: 'right' });
    await page.click('[data-testid="approve-application"]');

    // Verify status change
    await expect(applicationRow).toContainText('Approved');

    // Verify email notification
    await expect(page.locator('[data-testid="notification"]'))
      .toContainText('Application approved and email sent');
  });

  test('drag and drop status change', async ({ page }) => {
    await page.goto('/applications?view=grid');

    const application = page.locator('[data-testid="application-card"]').first();
    const reviewedColumn = page.locator('[data-testid="status-column-reviewed"]');

    await application.dragTo(reviewedColumn);

    await expect(reviewedColumn).toContainText('John Doe');
  });
});
```

### 6.5 Performance Optimization

#### Bundle Analysis and Optimization
```typescript
// apps/teams/next.config.ts
import { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    optimizePackageImports: [
      '@thf/ui',
      '@thf/teams-ui',
      '@thf/icons',
      'lucide-react',
      'motion',
    ],
  },

  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      config.optimization.splitChunks.chunks = 'all';
      config.optimization.splitChunks.cacheGroups = {
        ...config.optimization.splitChunks.cacheGroups,
        teams: {
          name: 'teams',
          test: /[\\/]packages[\\/]teams-/,
          priority: 30,
          reuseExistingChunk: true,
        },
        ui: {
          name: 'ui',
          test: /[\\/]packages[\\/](ui|icons|animations)[\\/]/,
          priority: 20,
          reuseExistingChunk: true,
        },
      };
    }
    return config;
  },

  images: {
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
  },
};

export default nextConfig;
```

#### Performance Monitoring
```typescript
// apps/teams/lib/performance.ts
export const performanceMetrics = {
  measurePageLoad: () => {
    if (typeof window !== 'undefined') {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime,
      };
    }
  },

  measureComponentRender: (componentName: string) => {
    performance.mark(`${componentName}-start`);
    return () => {
      performance.mark(`${componentName}-end`);
      performance.measure(componentName, `${componentName}-start`, `${componentName}-end`);
    };
  },
};
```

## Deployment Strategy

### 6.6 Deployment Configuration

#### Vercel Deployment Setup
```json
// apps/teams/vercel.json
{
  "buildCommand": "cd ../.. && npx turbo run build --filter=teams",
  "outputDirectory": ".next",
  "installCommand": "cd ../.. && bun install",
  "framework": "nextjs",
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key",
    "NEXT_PUBLIC_RESEND_API_KEY": "@resend-api-key",
    "NEXT_PUBLIC_APP_URL": "@teams-app-url"
  }
}
```

#### Environment Configuration
```bash
# apps/teams/.env.example
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Email Service
NEXT_PUBLIC_RESEND_API_KEY=your_resend_api_key

# Application URLs
NEXT_PUBLIC_APP_URL=https://teams.thehuefactory.co
NEXT_PUBLIC_MAIN_SITE_URL=https://thehuefactory.co

# Analytics (Optional)
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
```

### 6.7 Monitoring & Analytics

#### Error Tracking Setup
```typescript
// apps/teams/lib/monitoring.ts
import { captureException, captureMessage } from '@sentry/nextjs';

export const errorTracking = {
  logError: (error: Error, context?: Record<string, any>) => {
    console.error('Application Error:', error);
    captureException(error, { extra: context });
  },

  logInfo: (message: string, data?: Record<string, any>) => {
    console.info(message, data);
    captureMessage(message, 'info');
  },

  logPerformance: (metric: string, value: number) => {
    console.log(`Performance: ${metric} = ${value}ms`);
    // Send to analytics service
  },
};
```

## Risk Mitigation & Contingency Plans

### High-Risk Areas & Mitigation

#### 1. Package Dependencies Conflicts
**Risk**: Version conflicts between Circle-temp and team-thehuefactory dependencies
**Mitigation**:
- Use exact version pinning in package.json
- Implement comprehensive dependency audit
- Create compatibility testing matrix

#### 2. Performance Impact from Animations
**Risk**: Motion animations causing performance degradation
**Mitigation**:
- Implement `prefers-reduced-motion` support
- Use `will-change` CSS property strategically
- Lazy load animation components

#### 3. Database Migration Complexity
**Risk**: Data loss or corruption during Supabase integration
**Mitigation**:
- Complete database backup before migration
- Implement rollback procedures
- Use database transactions for critical operations

#### 4. Authentication Integration Issues
**Risk**: User session conflicts between old and new systems
**Mitigation**:
- Implement gradual user migration
- Maintain session compatibility layer
- Provide clear migration instructions

### Rollback Procedures

#### Emergency Rollback Plan
1. **Immediate**: Revert DNS to previous teams application
2. **Database**: Restore from pre-migration backup
3. **Code**: Deploy previous stable version
4. **Communication**: Notify users of temporary service restoration

#### Gradual Rollback Options
- Feature flags for progressive rollout
- A/B testing for user groups
- Canary deployments for risk mitigation

---

*This comprehensive implementation plan provides a structured approach to building the new teams application, combining sophisticated UI/UX design with complete team management functionality while maintaining high standards for performance, accessibility, and maintainability.*
