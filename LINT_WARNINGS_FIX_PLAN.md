# Lint Warnings Fix Plan

## Overview
This plan addresses the build warnings and lockfile issues in the monorepo. The warnings fall into several categories:
1. **Lockfile Issue**: Missing lockfile entry for '@emnapi/wasi-threads'
2. **TypeScript/ESLint Warnings**: Unused variables, missing prop types, unsafe operations
3. **React Warnings**: Missing prop validations, unknown DOM properties

## Priority Levels
- 🔴 **Critical**: Lockfile issues that could break builds
- 🟡 **High**: Type safety issues and potential runtime errors  
- 🟢 **Medium**: Code quality improvements (unused variables, prop types)

---

## 1. Lockfile Issues 🔴

### Issue: Missing '@emnapi/wasi-threads' lockfile entry
**Root Cause**: The dependency exists in the lockfile but Turbo can't calculate transitive closures properly.

**Solution**:
```bash
# Clean and regenerate lockfile
rm bun.lock
bun install
```

**Files Affected**: `bun.lock`

---

## 2. TypeScript/ESLint Warnings by File 🟡🟢

### `apps/web/app/(web)/company/page.tsx`
**Issue**: Unused variable 'isInView'
```typescript
// Line 26: 'isInView' is assigned a value but never used
```
**Solution**: Remove unused variable or implement the intended functionality

### `apps/web/components/admin/waitlist-table.tsx`
**Issues**:
- Lines 51, 60: `Unexpected any` types
- Line 118: Unsafe optional chain with non-null assertion

**Solutions**:
- Replace `any` types with proper TypeScript interfaces
- Remove non-null assertion or add proper null checks

### `apps/web/components/company-components/services-card.tsx`
**Issues**:
- Lines 24-31: Missing prop validations for React props
- Lines 35, 41, 43, 45: Unused variables (scrollYProgress, opacity, blur, blurFilter)
- Line 77: Missing prop validation for features.map

**Solutions**:
- Add PropTypes or convert to TypeScript interface
- Remove unused variables or implement intended functionality

### `apps/web/components/company-components/sticky-headers.tsx`
**Issues**:
- Line 2: Unused import 'useEffect'
- Line 5: Unused variable 'section'

**Solutions**:
- Remove unused imports and variables

### `apps/web/components/comps/home-parallax-hero.tsx`
**Issues**:
- Lines 17-18: Unused variables 'dy', 'my'

**Solutions**:
- Remove unused variables or implement intended functionality

### `apps/web/components/comps/project-component.tsx`
**Issues**:
- Line 192: Unused variables 'width', 'height'

**Solutions**:
- Remove unused variables or implement intended functionality

### `apps/web/components/forms/auth/login-form.tsx`
**Issues**:
- Line 39: Unused variable 'setSubmit'
- Line 47: Async promise executor (anti-pattern)
- Line 72: `any` type usage

**Solutions**:
- Remove unused variables
- Refactor async promise executor
- Add proper typing

### `apps/web/components/forms/join/` files (aff.tsx, coll.tsx, vol.tsx)
**Issues**:
- Multiple unused imports and variables
- Unused 'status' variables

**Solutions**:
- Clean up unused imports
- Remove or implement unused variables

### `apps/web/components/forms/notifications/index.tsx`
**Issues**:
- Line 60: Unnecessary escape characters in regex

**Solutions**:
- Fix regex pattern

### `apps/web/components/home-components/home-carousel.tsx`
**Issues**:
- Multiple unused imports (useAnimation, CarouselNext, CarouselPrevious)

**Solutions**:
- Remove unused imports

### `apps/web/components/layouts/navbar/` files
**Issues**:
- Multiple unused variables and imports

**Solutions**:
- Clean up unused code

### `apps/web/components/studio/` files
**Issues**:
- Unused variables and imports
- Unknown DOM properties (fill-rule, clip-rule)
- `any` type usage

**Solutions**:
- Clean up unused code
- Fix DOM property names (fillRule, clipRule)
- Add proper typing

### `apps/web/lib/urls.ts`
**Issues**:
- Lines 11, 13: Unused exports

**Solutions**:
- Remove unused exports or mark as used

---

## 3. Implementation Strategy

### Phase 1: Critical Issues 🔴
1. **Fix lockfile issue**
   ```bash
   rm bun.lock
   bun install
   ```

### Phase 2: High Priority Type Safety 🟡
1. **Replace `any` types** with proper interfaces
2. **Fix unsafe optional chaining**
3. **Fix async promise executors**

### Phase 3: Code Quality Improvements 🟢
1. **Remove unused variables and imports**
2. **Add missing prop types or convert to TypeScript interfaces**
3. **Fix DOM property names**
4. **Clean up regex patterns**

---

## 4. Automated Solutions

### ESLint Auto-fix
Many issues can be automatically fixed:
```bash
# Auto-fix what's possible
cd apps/web
bun run lint --fix
```

### TypeScript Strict Mode
Consider enabling stricter TypeScript settings in `tsconfig.json`:
```json
{
  "compilerOptions": {
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

---

## 5. Prevention Strategies

### Pre-commit Hooks
Add pre-commit hooks to prevent these issues:
```json
// package.json
{
  "scripts": {
    "pre-commit": "lint-staged"
  }
}
```

### ESLint Configuration Updates
Consider updating ESLint rules to catch these issues earlier:
```javascript
// eslint.config.js
export default {
  rules: {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "react/prop-types": "error"
  }
}
```

---

## 6. Execution Checklist

- [ ] Fix lockfile issue (Phase 1)
- [ ] Address type safety warnings (Phase 2)
- [ ] Clean up unused code (Phase 3)
- [ ] Run full build to verify fixes
- [ ] Update ESLint configuration for prevention
- [ ] Add pre-commit hooks

## Estimated Time
- **Phase 1**: 5 minutes
- **Phase 2**: 2-3 hours
- **Phase 3**: 1-2 hours
- **Total**: 3-5 hours

## Success Criteria
- ✅ Build completes without warnings
- ✅ All TypeScript errors resolved
- ✅ ESLint passes with 0 warnings
- ✅ No lockfile calculation issues
