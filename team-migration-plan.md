# Team Thehuefactory Migration Plan

## Executive Summary

This document outlines the comprehensive migration plan for integrating the `team-thehuefactory` directory into the main monorepo structure. The team application is a Next.js-based collaboration platform that manages team member applications, roles, NDAs, and administrative functions for The Hue Factory organization.

### Key Findings
- **Application Type**: Next.js 14.2.15 application with TypeScript
- **Primary Purpose**: Team member onboarding, role management, and administrative dashboard
- **Database**: Supabase integration with real-time subscriptions
- **Authentication**: Supabase Auth with role-based access control
- **UI Framework**: Tailwind CSS with Radix UI components
- **Email System**: Resend integration for automated notifications
- **Package Manager**: Bun (lockfile: bun.lockb)

## Detailed Functionality Breakdown

### 1. Authentication & User Management
**Files:**
- `lib/supabase/auth/client.ts` - Supabase client configuration
- `lib/supabase/auth/server.ts` - Server-side auth utilities
- `lib/supabase/auth/middleware.ts` - Auth middleware
- `lib/supabase/auth/getUser.ts` - User data fetching hook
- `components/forms/auth/login-form.tsx` - Login form component
- `middleware.ts` - Next.js middleware for route protection

**Functionality:**
- User authentication via Supabase Auth
- Role-based access control (Admin, Collaborator, Affiliate, Volunteer)
- Session management and route protection

### 2. Role-Based Dashboard System
**Files:**
- `app/(user)/dashboard/page.tsx` - Main dashboard with role-specific views
- `components/roles/constants.ts` - Role-based navigation configuration
- `components/roles/admin/` - Admin-specific components
- `components/roles/affiliate/` - Affiliate-specific components
- `components/app-sidebar.tsx` - Sidebar navigation component

**Functionality:**
- Admin dashboard with analytics and user management
- Role-specific navigation and permissions
- Real-time data updates via Supabase subscriptions

### 3. Application Management System
**Files:**
- `app/(user)/dashboard/applications/page.tsx` - Applications management
- `components/roles/admin/tables/application-table.tsx` - Applications data table
- `components/forms/volunteer-form.tsx` - Volunteer application form
- `lib/supabase/db/types/types.ts` - Database type definitions

**Functionality:**
- Team member application processing
- Multi-step application forms
- Application status tracking (received, reviewed, accepted/rejected)

### 4. Email Notification System
**Files:**
- `lib/emails/` directory structure:
  - `affiliates/` - Affiliate-specific email templates
  - `collaborators/` - Collaborator-specific email templates  
  - `volunteers/` - Volunteer-specific email templates
  - `launch-day.tsx` - Launch day notification template
  - `not-accepted.tsx` - Rejection notification template

**Functionality:**
- Automated email notifications for application status changes
- Role-specific email templates using React Email
- Integration with Resend email service

### 5. API Routes & Webhooks
**Files:**
- `app/(web)/api/collaborator/` - Collaborator management endpoints
- `app/(web)/api/affiliate/` - Affiliate management endpoints
- `app/(web)/api/volunteer/` - Volunteer management endpoints
- `app/(web)/api/ld/route.ts` - Launch day endpoint

**Functionality:**
- RESTful API endpoints for application management
- Email trigger endpoints for status changes
- Webhook handlers for external integrations

### 6. NDA Management
**Files:**
- `app/(user)/dashboard/nda/page.tsx` - NDA signing interface
- Database fields: `is_nda_signed`, `nda_signed_date` in JOIN_US_TABLE

**Functionality:**
- Digital NDA signing workflow
- NDA status tracking and enforcement
- Access control based on NDA completion

### 7. Waitlist Management
**Files:**
- `app/(user)/dashboard/waitlist/page.tsx` - Waitlist management
- `components/roles/admin/tables/waitlist-table.tsx` - Waitlist data table
- `components/roles/admin/charts/waitlists-charts.tsx` - Waitlist analytics

**Functionality:**
- Waitlist registration and management
- Launch day email campaign management
- Waitlist analytics and reporting

### 8. Proposal System
**Files:**
- `app/(user)/dashboard/proposals/page.tsx` - Proposals management
- `components/forms/proposal/index.tsx` - Proposal form
- `components/roles/admin/tables/proposal-table.tsx` - Proposals table
- `components/roles/affiliate/proposal-table.tsx` - Affiliate proposals

**Functionality:**
- Affiliate proposal submission system
- Proposal review and approval workflow
- Proposal tracking and management

## Database Schema Analysis

### Core Tables:
1. **JOIN_US_TABLE** - Main application data
2. **profiles** - User profile information
3. **affiliate_proposals** - Affiliate proposal data
4. **waitlists** - Waitlist registrations
5. **Emails** - Email tracking

### Key Enums:
- **Role Types**: Admin, Collaborator, Affiliate, Volunteer
- **is_accepted**: accepted, reviewing, notAccepted
- **is_reviewed**: reviewed, received, notAccepted

## File Mapping Table

| Current Location | Proposed New Location | Type | Priority |
|------------------|----------------------|------|----------|
| `team-thehuefactory/app/(user)/` | `apps/web/app/(team)/` | Route Group | High |
| `team-thehuefactory/components/roles/` | `packages/ui/src/team/roles/` | Components | High |
| `team-thehuefactory/components/forms/` | `packages/ui/src/team/forms/` | Components | High |
| `team-thehuefactory/lib/emails/` | `packages/emails/src/team/` | Email Templates | High |
| `team-thehuefactory/lib/supabase/` | `packages/supabase/src/team/` | Database Utils | High |
| `team-thehuefactory/app/(web)/api/` | `apps/web/app/api/team/` | API Routes | High |
| `team-thehuefactory/components/ui/` | `packages/ui/src/` | UI Components | Medium |
| `team-thehuefactory/lib/constants/` | `packages/constants/src/team/` | Constants | Medium |
| `team-thehuefactory/lib/utils/` | `packages/ui/src/utils/` | Utilities | Low |

## Migration Checklist

### Phase 1: Preparation (High Priority)
- [ ] Create team-specific route group in main app: `apps/web/app/(team)/`
- [ ] Set up team package structure in `packages/`
- [ ] Migrate database types and utilities to `packages/supabase/`
- [ ] Move email templates to `packages/emails/`

### Phase 2: Core Migration (High Priority)  
- [ ] Migrate authentication components and middleware
- [ ] Move role-based components to UI package
- [ ] Migrate API routes to main app structure
- [ ] Update import paths and dependencies

### Phase 3: Integration (Medium Priority)
- [ ] Integrate team routes with main app layout
- [ ] Merge UI components with existing design system
- [ ] Consolidate constants and configuration
- [ ] Update build and deployment scripts

### Phase 4: Testing & Optimization (Low Priority)
- [ ] Test all team functionality in new structure
- [ ] Optimize bundle size and performance
- [ ] Update documentation and README files
- [ ] Clean up unused dependencies

## Risk Assessment & Recommendations

### High Risk Areas:
1. **Database Dependencies**: Supabase configuration and types must be carefully migrated
2. **Authentication Flow**: Middleware and auth components need seamless integration
3. **Email Templates**: React Email components require proper package structure
4. **API Routes**: Endpoint paths will change, requiring frontend updates

### Recommendations:
1. **Incremental Migration**: Migrate in phases to minimize disruption
2. **Backup Strategy**: Create full backup before starting migration
3. **Testing Environment**: Set up staging environment for testing
4. **Documentation**: Update all documentation during migration
5. **Team Coordination**: Coordinate with team members using the system

### Dependencies to Address:
- Bun lockfile vs main repo package manager
- Next.js version compatibility (14.2.15 vs main app)
- Tailwind configuration merging
- Environment variable consolidation

## Next Steps

1. **Review and Approve Plan**: Get stakeholder approval for migration approach
2. **Set Up Development Environment**: Create feature branch for migration work
3. **Begin Phase 1**: Start with preparation tasks and package structure
4. **Coordinate Testing**: Plan testing strategy with current team users
5. **Schedule Migration Window**: Plan downtime if needed for database changes

## Detailed Component Inventory

### UI Components (team-thehuefactory/components/ui/)
- `accordion.tsx` - Collapsible content component
- `badge.tsx` - Status and label badges
- `button.tsx` - Primary button component
- `calendar.tsx` - Date picker calendar
- `card.tsx` - Content container cards
- `checkbox.tsx` - Form checkbox input
- `dialog.tsx` - Modal dialog component
- `form.tsx` - Form wrapper and validation
- `input.tsx` - Text input component
- `select.tsx` - Dropdown select component
- `table.tsx` - Data table component
- `textarea.tsx` - Multi-line text input

### UX Components (team-thehuefactory/components/ux/)
- `animations/` - Framer Motion animations
- `icons/` - Custom icon components
- `svgs/` - SVG graphic components

### Configuration Files
- `components.json` - Shadcn/ui configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration
- `next.config.mjs` - Next.js configuration
- `postcss.config.mjs` - PostCSS configuration

## Environment Variables Required

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Email Service (Resend)
NEXT_PUBLIC_RESEND_API_KEY=

# Application URLs
NEXT_PUBLIC_APP_URL=
NEXT_PUBLIC_TEAM_URL=
```

## Package Dependencies Analysis

### Core Dependencies (from team-thehuefactory/package.json):
```json
{
  "@hookform/resolvers": "^3.9.0",
  "@radix-ui/react-*": "Various versions",
  "@supabase/ssr": "^0.5.1",
  "@tanstack/react-table": "^8.20.5",
  "framer-motion": "^11.11.4",
  "next": "14.2.15",
  "react-hook-form": "^7.53.0",
  "resend": "^4.0.0",
  "zod": "^3.23.8",
  "zustand": "^5.0.0-rc.2"
}
```

### Potential Conflicts with Main Repo:
- Next.js version differences
- React version compatibility
- Tailwind CSS configuration merging
- TypeScript configuration alignment

## Migration Implementation Steps

### Step 1: Package Structure Setup
```bash
# Create team-specific packages
mkdir -p packages/team-ui/src
mkdir -p packages/team-emails/src
mkdir -p packages/team-supabase/src
mkdir -p packages/team-constants/src

# Create team route group in main app
mkdir -p apps/web/app/(team)
```

### Step 2: Database Migration
1. Export current Supabase schema and types
2. Integrate with main repo's Supabase configuration
3. Update type definitions in packages/supabase
4. Test database connectivity and queries

### Step 3: Component Migration Priority Order
1. **Critical Path Components** (Authentication, Dashboard)
2. **Form Components** (Application forms, NDA forms)
3. **Admin Components** (Tables, Charts, Management)
4. **Email Templates** (Notification system)
5. **Utility Components** (UI components, helpers)

### Step 4: API Route Migration
```bash
# Current structure
team-thehuefactory/app/(web)/api/

# New structure
apps/web/app/api/team/
├── collaborator/
├── affiliate/
├── volunteer/
└── ld/
```

## Testing Strategy

### Unit Testing
- Test all migrated components individually
- Verify form validation and submission
- Test authentication flows
- Validate email template rendering

### Integration Testing
- Test complete user workflows
- Verify database operations
- Test email sending functionality
- Validate role-based access control

### User Acceptance Testing
- Test with actual team members
- Verify all existing functionality works
- Test new integrated workflows
- Validate performance and usability

## Rollback Plan

### Backup Strategy
1. Create full database backup before migration
2. Tag current codebase state
3. Document all configuration changes
4. Maintain parallel deployment capability

### Rollback Triggers
- Critical functionality broken
- Performance degradation > 50%
- User authentication failures
- Data integrity issues

### Rollback Process
1. Revert to tagged codebase version
2. Restore database from backup
3. Update DNS/routing if needed
4. Notify stakeholders of rollback

## Post-Migration Optimization

### Performance Improvements
- Bundle size optimization
- Code splitting for team routes
- Image optimization
- Database query optimization

### Maintenance Tasks
- Update documentation
- Clean up unused dependencies
- Consolidate duplicate code
- Update deployment scripts

---

*This comprehensive migration plan ensures no team functionality is lost during the consolidation into the main monorepo structure while maintaining system integrity and user experience.*
