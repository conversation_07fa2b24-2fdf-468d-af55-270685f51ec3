# Circle - Project Management Interface


i want to migrate the ui and functionality of the cirle-temp project into a new repo in this mono repo called teams and it will have the following features which will be build using the exsting components and ui element in the circle-temp that will be migrated to the packages in the monorepo to be used globaly 


## Overview
Circle is a modern project management interface inspired by Linear, built with Next.js and shadcn/ui. It provides comprehensive issue tracking, project management, and team collaboration features with a responsive, dark-themed UI.

## Technology Stack

### Core Technologies
- **Framework**: Next.js 15.2.4 (App Router)
- **Language**: TypeScript
- **UI Components**: shadcn/ui (Radix UI primitives)
- **Styling**: Tailwind CSS v4
- **State Management**: Zustand
- **Animations**: Motion (Framer Motion)
- **Drag & Drop**: React DnD
- **Icons**: Lucide React, Remix Icons

### Key Dependencies
- **Form Handling**: React Hook Form + Zod validation
- **Date Handling**: date-fns, React Day Picker
- **Ranking System**: LexoRank algorithm for issue ordering
- **Notifications**: <PERSON><PERSON> (toast notifications)
- **Theme**: next-themes for dark/light mode

## Project Structure

### Root Directory
```
├── app/                    # Next.js App Router pages
├── components/             # React components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility functions
├── mock-data/              # Mock data and interfaces
├── public/                 # Static assets
├── store/                  # Zustand state management
├── components.json         # shadcn/ui configuration
├── next.config.ts          # Next.js configuration
├── package.json            # Dependencies and scripts
├── tailwind.config.js      # Tailwind CSS configuration
└── tsconfig.json           # TypeScript configuration
```

## App Router Structure

### Pages & Routes
```
app/
├── layout.tsx              # Root layout with theme provider
├── page.tsx                # Root redirect to default team
├── not-found.tsx           # 404 page
├── globals.css             # Global styles
└── [orgId]/                # Organization-scoped routes
    ├── page.tsx            # Organization home
    ├── teams/
    │   └── page.tsx        # Teams listing
    ├── projects/
    │   └── page.tsx        # Projects listing
    ├── members/
    │   └── page.tsx        # Members listing
    ├── settings/
    │   └── page.tsx        # Settings page
    └── team/
        └── [teamId]/
            └── all/
                └── page.tsx # Team issues view
```

### Routing Pattern
- **Organization-scoped**: All routes are under `[orgId]` (e.g., `/lndev-ui/`)
- **Team-specific**: Issues are organized by team (`/lndev-ui/team/CORE/all`)
- **Resource pages**: Dedicated pages for teams, projects, members, settings

## Components Architecture

### Layout Components
```
components/layout/
├── main-layout.tsx         # Main application layout wrapper
├── theme-provider.tsx      # Theme context provider
├── theme-toggle.tsx        # Dark/light mode toggle
├── headers/                # Page-specific headers
│   ├── issues/             # Issue page headers with filters
│   ├── teams/              # Team page headers
│   ├── projects/           # Project page headers
│   └── members/            # Member page headers
└── sidebar/                # Application sidebar
    ├── app-sidebar.tsx     # Main sidebar component
    ├── org-switcher.tsx    # Organization switcher
    ├── nav-*.tsx           # Navigation sections
    └── create-new-issue/   # Issue creation modal
```

### Feature Components
```
components/common/
├── issues/                 # Issue management components
│   ├── all-issues.tsx      # Main issues container
│   ├── group-issues.tsx    # Issues grouped by status
│   ├── issue-line.tsx      # Individual issue row
│   ├── issue-grid.tsx      # Grid view with drag & drop
│   ├── search-issues.tsx   # Issue search functionality
│   ├── status-selector.tsx # Status change dropdown
│   ├── priority-selector.tsx # Priority change dropdown
│   └── create-issue-modal-provider.tsx # Issue creation modal
├── teams/                  # Team management
│   ├── teams.tsx           # Teams listing
│   ├── team-line.tsx       # Individual team row
│   └── members-tooltip.tsx # Team members tooltip
├── projects/               # Project management
│   ├── projects.tsx        # Projects listing
│   └── project-line.tsx    # Individual project row
├── members/                # Member management
│   ├── members.tsx         # Members listing
│   └── member-line.tsx     # Individual member row
└── settings/               # Settings and configuration
    └── settings.tsx        # Settings page with features/integrations
```

### UI Components (shadcn/ui)
```
components/ui/
├── button.tsx              # Button variants
├── dialog.tsx              # Modal dialogs
├── dropdown-menu.tsx       # Dropdown menus
├── command.tsx             # Command palette
├── popover.tsx             # Popover components
├── sidebar.tsx             # Sidebar primitives
├── avatar.tsx              # User avatars
├── badge.tsx               # Status badges
├── progress.tsx            # Progress bars
├── table.tsx               # Data tables
├── form.tsx                # Form components
├── input.tsx               # Input fields
├── textarea.tsx            # Text areas
├── select.tsx              # Select dropdowns
├── checkbox.tsx            # Checkboxes
├── switch.tsx              # Toggle switches
├── tooltip.tsx             # Tooltips
├── context-menu.tsx        # Right-click menus
├── alert-dialog.tsx        # Confirmation dialogs
├── tabs.tsx                # Tab navigation
├── calendar.tsx            # Date picker
├── card.tsx                # Card containers
├── separator.tsx           # Visual separators
├── skeleton.tsx            # Loading skeletons
├── sonner.tsx              # Toast notifications
└── collapsible.tsx         # Collapsible sections
```

## State Management (Zustand)

### Store Structure
```
store/
├── issues-store.ts         # Issue management state
├── create-issue-store.ts   # Issue creation modal state
├── search-store.ts         # Search functionality state
├── filter-store.ts         # Issue filtering state
└── view-store.ts           # View type state (list/grid)
```

### Store Responsibilities

#### Issues Store (`issues-store.ts`)
- **Data**: Issues array, grouped issues by status
- **Actions**: Add, update, delete issues
- **Filters**: Filter by status, priority, assignee, labels, project
- **Search**: Full-text search across issues
- **Status Management**: Update issue status with drag & drop support

#### Filter Store (`filter-store.ts`)
- **State**: Active filters for status, assignee, priority, labels, project
- **Actions**: Toggle filters, clear filters, set multiple filters
- **Utilities**: Check if filters are active, count active filters

#### Search Store (`search-store.ts`)
- **State**: Search modal open/closed, search query
- **Actions**: Open/close search, set query, reset search

#### View Store (`view-store.ts`)
- **State**: Current view type (list or grid)
- **Actions**: Switch between list and grid views

#### Create Issue Store (`create-issue-store.ts`)
- **State**: Modal open/closed, default status for new issues
- **Actions**: Open modal with optional default status, close modal

## Data Models & Mock Data

### Core Interfaces
```typescript
// User Management
interface User {
  id: string;
  name: string;
  avatarUrl: string;
  email: string;
  status: 'online' | 'offline' | 'away';
  role: 'Member' | 'Admin' | 'Guest';
  joinedDate: string;
  teamIds: string[];
}

// Issue Management
interface Issue {
  id: string;
  identifier: string;        // e.g., "LNUI-101"
  title: string;
  description: string;
  status: Status;
  assignee: User | null;
  priority: Priority;
  labels: LabelInterface[];
  createdAt: string;
  cycleId: string;
  project?: Project;
  subissues?: string[];
  rank: string;             // LexoRank for ordering
  dueDate?: string;
}

// Project Management
interface Project {
  id: string;
  name: string;
  status: Status;
  icon: LucideIcon | RemixiconComponentType;
  percentComplete: number;
  startDate: string;
  lead: User;
  priority: Priority;
  health: Health;
}

// Team Management
interface Team {
  id: string;
  name: string;
  icon: string;
  joined: boolean;
  color: string;
  members: User[];
  projects: Project[];
}
```

### Mock Data Files
```
mock-data/
├── users.ts               # User profiles and team memberships
├── issues.ts              # Issue data with LexoRank ordering
├── projects.ts            # Project data with health status
├── teams.ts               # Team structure and memberships
├── cycles.ts              # Sprint/cycle information
├── labels.ts              # Issue labels with colors
├── priorities.tsx         # Priority levels with custom icons
├── status.tsx             # Issue status with custom icons
└── side-bar-nav.ts        # Navigation menu structure
```

## Key Features

### Issue Management
- **Views**: List and grid views with drag & drop
- **Filtering**: Multi-criteria filtering (status, assignee, priority, labels, project)
- **Search**: Real-time search across all issues
- **Status Management**: Visual status indicators with drag & drop updates
- **Priority System**: 5-level priority system with custom icons
- **Labels**: Color-coded labels for categorization
- **Ranking**: LexoRank algorithm for consistent issue ordering

### Project Management
- **Health Tracking**: Project health indicators (on-track, at-risk, off-track)
- **Progress Tracking**: Percentage completion tracking
- **Lead Assignment**: Project lead management
- **Status Workflow**: Project status progression

### Team Management
- **Team Structure**: Hierarchical team organization
- **Membership**: Join/leave team functionality
- **Member Profiles**: User status, roles, and team associations
- **Project Association**: Teams linked to multiple projects

### User Interface
- **Responsive Design**: Mobile-first responsive layout
- **Dark Theme**: Default dark theme with system preference support
- **Animations**: Smooth transitions and micro-interactions
- **Accessibility**: ARIA labels and keyboard navigation
- **Context Menus**: Right-click actions throughout the interface

### Navigation & Layout
- **Sidebar Navigation**: Collapsible sidebar with organization switching
- **Breadcrumb Navigation**: Clear page hierarchy
- **Search Command**: Global search with keyboard shortcuts
- **Settings Panel**: Workspace configuration and integrations

## Development Features

### Code Quality
- **TypeScript**: Full type safety throughout the application
- **ESLint**: Code linting with Prettier formatting
- **Husky**: Git hooks for pre-commit validation
- **Lint Staged**: Staged file linting

### Performance
- **Next.js App Router**: Server-side rendering and routing
- **Turbopack**: Fast development builds
- **Code Splitting**: Automatic component-level code splitting
- **Image Optimization**: Next.js image optimization

### Developer Experience
- **Hot Reload**: Fast development iteration
- **TypeScript Strict Mode**: Enhanced type checking
- **Path Aliases**: Clean import paths with @ prefix
- **Component Library**: Consistent UI components with shadcn/ui
