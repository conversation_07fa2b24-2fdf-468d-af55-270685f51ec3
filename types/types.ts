import { AnimationProps, Variant<PERSON>abe<PERSON> } from 'framer-motion';

export type motionType = AnimationProps | VariantLabels | undefined;

export type pageProps = {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export type img = {
  _key: string;
  _type: string;
  _version: number;
  id: string;
  public_id: string;
  format: string;
  created_at: string;
  access_mode: string;
  resource_type: string;
  width: number;
  height: number;
  access_control: [];
  tags: [];
  asset_folder: string;
  created_by: { id: string; type: string };
  duration: null;
  uploaded_by: { type: string; id: string };
  bytes: number;
  version: number;
  folder_id: string;
  type: string;
  display_name: string;
  url: string;
  secure_url: string;
};

export type vid = {
  _key: string;
  _type: string;
  _version: number;
  access_control: [];
  uploaded_by: { id: string; type: string };
  tags: [];
  width: number;
  height: number;
  asset_folder: string;
  public_id: string;
  id: string;
  bytes: number;
  duration: number;
  version: number;
  created_by: { id: string; type: string };
  access_mode: string;
  resource_type: string;
  display_name: string;
  metadata: {};
  type: string;
  format: string;
  created_at: string;
  folder_id: string;
  url: string;
  secure_url: string;
};

export type brandproject = {
  _id: string;
  projectype: string;
  title: string;
  slug: string;
  subtitle: string;
  intro: string;
  projectlink: string;
  publishedAt: string;
  ogImage: img | vid;
  carouselLandscape: img | vid;
  carouselPortrait: img | vid;
  coverPortraitImage: img | vid;
  allProjectsLandscape: img | vid;
  coverImage: img | vid;
  row_one: img | vid;
  row_two: img | vid;
  row_three_first_column: img | vid;
  row_three_second_column: img | vid;
  row_four: img | vid;
  row_five_first_column: img | vid;
  row_five_second_column_one: img | vid;
  row_five_second_column_two: img | vid;
  row_six: img | vid;
  body: [Object][];
};

export type brandprojectwc = {
  _id: string;
  projectype: string;
  title: string;
  slug: string;
  subtitle: string;
  publishedAt: string;
};

export type docs = {
  _id: string;
  title: string;
  slug: string;
  subtitle: string;
  publishedAt: string;
  body: [Object][];
};

export type docswc = {
  _id: string;
  title: string;
  slug: string;
};
