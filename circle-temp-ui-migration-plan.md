# Circle-Temp UI/UX Migration Plan

## Executive Summary

This document outlines the UI/UX-focused migration plan for integrating the visual design and selective functionality from the `circle-temp` directory into the main monorepo. Circle-temp is a Linear-inspired project management interface with a sophisticated dark-themed design system, modern animations, and polished user experience patterns.

### Key Visual Characteristics
- **Design Language**: Linear-inspired, minimalist, professional
- **Color Scheme**: Dark-first design with sophisticated OKLCH color system
- **Typography**: Geist Sans/Mono font family with careful hierarchy
- **Animations**: Smooth Motion (Framer Motion) transitions and micro-interactions
- **Layout**: Sidebar-driven navigation with responsive grid/list views
- **Interaction Patterns**: Drag & drop, context menus, keyboard shortcuts

## Design System Documentation

### Color Palette (OKLCH Color System)

#### Dark Theme (Primary)
```css
--background: oklch(0.141 0.005 285.823)     /* Deep dark blue-gray */
--foreground: oklch(0.985 0 0)               /* Pure white text */
--card: oklch(0.141 0.005 285.823)           /* Card backgrounds */
--primary: oklch(0.985 0 0)                  /* White primary */
--secondary: oklch(0.274 0.006 286.033)      /* Medium gray */
--muted: oklch(0.274 0.006 286.033)          /* Muted backgrounds */
--accent: oklch(0.274 0.006 286.033)         /* Accent elements */
--border: oklch(0.274 0.006 286.033)         /* Border color */
--sidebar: oklch(0.21 0.006 285.885)         /* Sidebar background */
--sidebar-primary: oklch(0.488 0.243 264.376) /* Sidebar accent blue */
--container: #101011                         /* Container background */
```

#### Status Colors
```css
--status-in-progress: #facc15    /* Yellow */
--status-technical-review: #22c55e /* Green */
--status-completed: #8b5cf6      /* Purple */
--status-paused: #0ea5e9         /* Blue */
--status-todo: #f97316           /* Orange */
--status-backlog: #ec4899        /* Pink */
```

#### Priority Colors
- **Urgent**: Red with filled square icon
- **High**: Orange with 3-bar chart icon
- **Medium**: Yellow with 2-bar chart icon  
- **Low**: Gray with 1-bar chart icon
- **No Priority**: Dotted line icon

### Typography System

#### Font Stack
```css
--font-sans: var(--font-geist-sans)  /* Primary font */
--font-mono: var(--font-geist-mono)  /* Code/monospace */
```

#### Text Hierarchy
- **Issue Titles**: `text-xs sm:text-sm font-medium sm:font-semibold`
- **Identifiers**: `text-sm text-muted-foreground font-medium w-[66px]`
- **Dates**: `text-xs text-muted-foreground`
- **Navigation**: `text-sm font-medium`
- **Headers**: `text-lg font-semibold leading-tight`

### Spacing & Layout System

#### Border Radius
```css
--radius: 0.625rem (10px)
--radius-sm: calc(var(--radius) - 4px)  /* 6px */
--radius-md: calc(var(--radius) - 2px)  /* 8px */
--radius-lg: var(--radius)              /* 10px */
--radius-xl: calc(var(--radius) + 4px)  /* 14px */
```

#### Component Spacing
- **Issue Lines**: `h-11 px-6` (44px height, 24px horizontal padding)
- **Sidebar**: Collapsible with smooth transitions
- **Grid Gaps**: `gap-0.5`, `gap-2`, `gap-4` for different contexts
- **Container Padding**: `lg:p-2` for outer container

## UI Component Inventory

### Core Layout Components

#### 1. MainLayout (`components/layout/main-layout.tsx`)
**Visual Features:**
- Responsive height calculations: `h-[calc(100svh-40px)] lg:h-[calc(100svh-56px)]`
- Container styling: `lg:border lg:rounded-md overflow-hidden bg-container`
- Flexible header system with 1 or 2 header support

#### 2. AppSidebar (`components/layout/sidebar/app-sidebar.tsx`)
**Visual Features:**
- Collapsible offcanvas behavior
- Conditional content based on settings/main views
- Footer with promotional card and action buttons
- Smooth transitions and hover states

#### 3. Theme System
**Visual Features:**
- Dark-first design with system preference support
- OKLCH color space for better color perception
- Smooth theme transitions
- Custom search input styling with SVG icons

### Issue Management Components

#### 1. IssueLine (`components/common/issues/issue-line.tsx`)
**Visual Features:**
- Hover state: `hover:bg-sidebar/50`
- Motion layout animations: `layoutId` support
- Responsive text sizing: `text-xs sm:text-sm`
- Stacked badge layout: `-space-x-5 hover:space-x-1`
- Context menu integration

#### 2. Status Icons (Custom SVG Components)
**Visual Features:**
- Circular progress indicators with dashed strokes
- Color-coded status representation
- Animated progress fills
- Consistent 14x14px sizing
- Custom stroke patterns for each status

#### 3. Priority Icons (Custom SVG Components)
**Visual Features:**
- Bar chart style for priority levels
- Opacity variations for visual hierarchy
- Urgent priority uses filled square with exclamation
- Consistent 16x16px sizing

### Interactive Elements

#### 1. Button System (`components/ui/button.tsx`)
**Visual Features:**
- Comprehensive variant system: `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`
- Size variants: `xxs`, `xs`, `sm`, `default`, `lg`, `icon`
- Focus states with ring styling
- Shadow system: `shadow-xs`
- Icon integration with automatic sizing

#### 2. Drag & Drop Interface
**Visual Features:**
- Smooth drag animations
- Visual feedback during drag operations
- Grid and list view support
- LexoRank ordering system integration

#### 3. Context Menus
**Visual Features:**
- Right-click context menus throughout interface
- Consistent styling with main UI
- Action-specific menu items

## Animation & Interaction Patterns

### Motion System (Framer Motion)
```typescript
// Layout animations for issue transitions
<motion.div layoutId={`issue-line-${issue.identifier}`} />

// Hover state transitions
transition-[color,box-shadow]
duration-200 transition-all

// Spacing animations
-space-x-5 hover:space-x-1 lg:space-x-1
```

### Micro-Interactions
- **Badge Stacking**: Overlapped badges that separate on hover
- **Status Transitions**: Smooth color changes on status updates
- **Sidebar Collapse**: Smooth width transitions
- **Search Input**: Custom cancel button with SVG styling

## Component-to-Component Mapping

### High Priority UI Migrations

| Circle-Temp Component | Target Location | Visual Priority | Integration Notes |
|----------------------|-----------------|-----------------|-------------------|
| `MainLayout` | `packages/ui/src/layouts/` | **Critical** | Core layout system |
| `AppSidebar` | `packages/ui/src/navigation/` | **Critical** | Navigation foundation |
| `IssueLine` | `packages/ui/src/data-display/` | **High** | List item pattern |
| Status Icons | `packages/ui/src/icons/status/` | **High** | Custom SVG system |
| Priority Icons | `packages/ui/src/icons/priority/` | **High** | Custom SVG system |
| `Button` variants | `packages/ui/src/button/` | **High** | Enhanced button system |
| Theme Provider | `packages/ui/src/theme/` | **Critical** | Dark-first theming |

### Medium Priority UI Migrations

| Circle-Temp Component | Target Location | Visual Priority | Integration Notes |
|----------------------|-----------------|-----------------|-------------------|
| Grid/List Views | `packages/ui/src/data-display/` | **Medium** | View switching pattern |
| Context Menus | `packages/ui/src/overlays/` | **Medium** | Right-click interactions |
| Search Components | `packages/ui/src/forms/` | **Medium** | Enhanced search UX |
| Badge System | `packages/ui/src/data-display/` | **Medium** | Label/status badges |
| Progress Indicators | `packages/ui/src/feedback/` | **Medium** | Status progress |

## Selective Functionality Integration

### Core Features to Blend with Team System

#### 1. Issue Management Patterns
**From Circle-Temp:**
- Visual issue representation (identifier, title, status, priority)
- Drag & drop status updates
- Context menu actions
- Grid/list view switching

**Integration with Team System:**
- Apply to team member applications (status: received → reviewed → accepted)
- Use for project/task management within teams
- Enhance team dashboard with issue-style cards

#### 2. Status Workflow System
**From Circle-Temp:**
- Visual status indicators with progress
- Color-coded status system
- Smooth status transitions

**Integration with Team System:**
- Team member application workflow
- Project status tracking
- NDA signing status
- Proposal review status

#### 3. Priority & Labeling System
**From Circle-Temp:**
- 5-level priority system with custom icons
- Color-coded labels
- Badge stacking patterns

**Integration with Team System:**
- Application priority levels
- Team member skill labels
- Project priority indicators
- Role-based badges

#### 4. Search & Filtering
**From Circle-Temp:**
- Global search with keyboard shortcuts
- Multi-criteria filtering
- Real-time search results

**Integration with Team System:**
- Search team members by skills/role
- Filter applications by status/role
- Search proposals and projects

## Visual Design Preservation Checklist

### ✅ Critical Visual Elements
- [ ] **Color System**: Migrate complete OKLCH color palette
- [ ] **Typography**: Implement Geist font family
- [ ] **Dark Theme**: Preserve dark-first design approach
- [ ] **Border Radius**: Maintain 10px base radius system
- [ ] **Spacing**: Preserve component spacing patterns
- [ ] **Status Icons**: Migrate custom SVG status indicators
- [ ] **Priority Icons**: Migrate custom SVG priority indicators

### ✅ Layout & Structure
- [ ] **Sidebar Navigation**: Preserve collapsible sidebar pattern
- [ ] **Main Layout**: Maintain responsive container system
- [ ] **Grid System**: Preserve responsive grid/list patterns
- [ ] **Card Design**: Maintain container styling approach
- [ ] **Header System**: Preserve flexible header patterns

### ✅ Interactive Elements
- [ ] **Button Variants**: Migrate comprehensive button system
- [ ] **Hover States**: Preserve subtle hover interactions
- [ ] **Focus States**: Maintain accessibility focus styling
- [ ] **Transitions**: Preserve smooth animation patterns
- [ ] **Context Menus**: Maintain right-click interaction patterns

### ✅ Animation & Motion
- [ ] **Layout Animations**: Preserve Motion layout transitions
- [ ] **Micro-interactions**: Maintain badge stacking, spacing animations
- [ ] **Status Transitions**: Preserve smooth color changes
- [ ] **Drag & Drop**: Maintain visual feedback patterns

## Integration Strategy

### Phase 1: Design System Foundation
1. **Color System Migration**: Implement OKLCH color variables in main theme
2. **Typography Setup**: Add Geist fonts to main application
3. **Component Base**: Migrate core UI components to packages/ui
4. **Theme Provider**: Integrate dark-first theme system

### Phase 2: Layout Integration
1. **Sidebar Pattern**: Adapt sidebar navigation for team context
2. **Main Layout**: Integrate responsive layout system
3. **Header System**: Merge with existing team headers
4. **Container Styling**: Apply container patterns to team pages

### Phase 3: Interactive Components
1. **Enhanced Buttons**: Upgrade team app buttons with Circle variants
2. **Status System**: Apply visual status patterns to team workflows
3. **Priority System**: Integrate priority indicators for team features
4. **Search Enhancement**: Upgrade team search with Circle patterns

### Phase 4: Advanced Features
1. **Drag & Drop**: Add drag & drop to team member management
2. **Context Menus**: Enhance team interfaces with right-click actions
3. **Animation Polish**: Add Motion animations to team interactions
4. **Grid/List Views**: Provide view switching for team data

## Risk Assessment

### High Risk Areas
1. **Color Conflicts**: OKLCH vs existing color systems
2. **Font Loading**: Geist fonts vs existing typography
3. **Animation Performance**: Motion library integration
4. **Theme Switching**: Dark-first vs existing theme approach

### Mitigation Strategies
1. **Gradual Migration**: Implement design system incrementally
2. **Fallback Fonts**: Ensure graceful font fallbacks
3. **Performance Testing**: Monitor animation performance impact
4. **Theme Compatibility**: Ensure light theme still works

## Detailed Visual Component Analysis

### Custom Icon System Deep Dive

#### Status Icons Visual Specifications
```typescript
// Backlog Icon - Dashed circle with empty center
<circle cx="7" cy="7" r="6" stroke="#bec2c8" strokeWidth="2"
        strokeDasharray="1.4 1.74" strokeDashoffset="0.65" />

// In Progress Icon - Yellow with partial fill
<circle cx="7" cy="7" r="6" stroke="#facc15" strokeWidth="2" />
<circle cx="7" cy="7" r="2" stroke="#facc15" strokeWidth="4"
        strokeDasharray="2.0839231268812295 100" />

// Completed Icon - Purple with checkmark
<circle cx="7" cy="7" r="6" stroke="#8b5cf6" strokeWidth="2" />
<path d="M4.5 7L6.5 9L9.5 5" stroke="#8b5cf6" strokeWidth="1.5" />
```

#### Priority Icons Visual Specifications
```typescript
// High Priority - 3 ascending bars
<rect x="1.5" y="8" width="3" height="6" rx="1" />
<rect x="6.5" y="5" width="3" height="9" rx="1" />
<rect x="11.5" y="2" width="3" height="12" rx="1" />

// Medium Priority - 2 bars + faded third
<rect x="11.5" y="2" width="3" height="12" rx="1" fillOpacity="0.4" />

// Urgent Priority - Filled square with exclamation
<path d="M3 1C1.91067 1 1 1.91067 1 3V13C1 14.0893..." />
```

### Layout Pattern Specifications

#### Sidebar Visual Behavior
```css
/* Collapsible states */
.sidebar-collapsed { width: 48px; }
.sidebar-expanded { width: 240px; }

/* Transition timing */
transition: width 200ms cubic-bezier(0.4, 0, 0.2, 1);

/* Visual hierarchy */
.sidebar-header { height: 56px; padding: 12px; }
.sidebar-content { flex: 1; overflow-y: auto; }
.sidebar-footer { padding: 16px; }
```

#### Issue Line Visual Pattern
```css
/* Base styling */
.issue-line {
  height: 44px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  transition: background-color 150ms ease;
}

/* Hover state */
.issue-line:hover {
  background-color: rgba(var(--sidebar), 0.5);
}

/* Badge stacking animation */
.badge-stack {
  margin-left: -20px; /* -space-x-5 */
  transition: margin-left 200ms ease;
}

.badge-stack:hover {
  margin-left: 4px; /* space-x-1 */
}
```

### Responsive Design Patterns

#### Breakpoint System
```css
/* Mobile-first responsive patterns */
.text-responsive {
  font-size: 0.75rem; /* text-xs */
}

@media (min-width: 640px) {
  .text-responsive {
    font-size: 0.875rem; /* sm:text-sm */
    font-weight: 600; /* sm:font-semibold */
  }
}

/* Visibility patterns */
.mobile-hidden { display: none; }
@media (min-width: 640px) {
  .mobile-hidden { display: inline-block; }
}
```

#### Container Responsive Behavior
```css
/* Mobile: Full width, no padding */
.main-container {
  height: 100svh;
  overflow: hidden;
  width: 100%;
}

/* Desktop: Padded with border */
@media (min-width: 1024px) {
  .main-container {
    padding: 8px;
  }

  .inner-container {
    border: 1px solid var(--border);
    border-radius: 6px;
    overflow: hidden;
  }
}
```

## Advanced Interaction Patterns

### Drag & Drop Visual Feedback
```typescript
// Visual states during drag operations
interface DragStates {
  isDragging: boolean;
  isOver: boolean;
  canDrop: boolean;
}

// Visual feedback classes
.drag-source { opacity: 0.5; transform: rotate(5deg); }
.drop-target { background: rgba(var(--primary), 0.1); }
.drop-active { border: 2px dashed var(--primary); }
```

### Context Menu Styling
```css
.context-menu {
  background: var(--popover);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 4px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.context-menu-item {
  padding: 8px 12px;
  border-radius: calc(var(--radius) - 2px);
  transition: background-color 150ms ease;
}

.context-menu-item:hover {
  background: var(--accent);
}
```

### Search Input Enhancement
```css
/* Custom search cancel button */
input::-webkit-search-cancel-button {
  -webkit-appearance: none;
  height: 16px;
  width: 16px;
  background-image: url("data:image/svg+xml;utf8,<svg>...</svg>");
  background-size: 16px 16px;
  cursor: pointer;
}

/* Dark theme variant */
.dark input::-webkit-search-cancel-button {
  background-image: url("data:image/svg+xml;utf8,<svg stroke='%239E9FAA'>...</svg>");
}

/* Font size override for mobile */
input[type='search'] {
  font-size: 16px !important; /* Prevents zoom on iOS */
}
```

## State Management Integration

### Visual State Patterns
```typescript
// Issue state with visual properties
interface IssueState {
  id: string;
  status: {
    id: string;
    color: string;
    icon: React.FC;
  };
  priority: {
    id: string;
    icon: React.FC;
  };
  visualState: {
    isSelected: boolean;
    isDragging: boolean;
    isHovered: boolean;
  };
}
```

### Animation State Management
```typescript
// Motion layout IDs for smooth transitions
const layoutIds = {
  issueLine: `issue-line-${issue.identifier}`,
  statusBadge: `status-${issue.id}`,
  priorityIcon: `priority-${issue.id}`,
};

// Stagger animations for lists
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
    },
  },
};
```

## Performance Optimization Patterns

### Virtualization for Large Lists
```typescript
// Virtual scrolling for issue lists
interface VirtualizedListProps {
  items: Issue[];
  itemHeight: 44; // Fixed height for issue lines
  containerHeight: number;
  overscan: 5; // Render extra items for smooth scrolling
}
```

### Lazy Loading Patterns
```typescript
// Progressive image loading for avatars
const Avatar = ({ src, alt }: AvatarProps) => {
  const [loaded, setLoaded] = useState(false);

  return (
    <div className="relative">
      {!loaded && <Skeleton className="w-8 h-8 rounded-full" />}
      <img
        src={src}
        alt={alt}
        onLoad={() => setLoaded(true)}
        className={cn("w-8 h-8 rounded-full", loaded ? "opacity-100" : "opacity-0")}
      />
    </div>
  );
};
```

## Accessibility Enhancements

### Keyboard Navigation Patterns
```typescript
// Keyboard shortcuts for issue management
const keyboardShortcuts = {
  'cmd+k': 'Open search',
  'c': 'Create new issue',
  'j': 'Next issue',
  'k': 'Previous issue',
  'enter': 'Open issue',
  'escape': 'Close modal',
};

// Focus management for modals
const useFocusTrap = (isOpen: boolean) => {
  useEffect(() => {
    if (isOpen) {
      const focusableElements = document.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      // Focus trap implementation
    }
  }, [isOpen]);
};
```

### Screen Reader Support
```typescript
// ARIA labels for status icons
const StatusIcon = ({ status }: { status: Status }) => (
  <div
    role="img"
    aria-label={`Status: ${status.name}`}
    className="flex items-center"
  >
    <status.icon />
  </div>
);

// Live regions for dynamic updates
<div aria-live="polite" aria-atomic="true" className="sr-only">
  {statusUpdateMessage}
</div>
```

## Implementation Roadmap

### Week 1-2: Foundation
- [ ] Migrate OKLCH color system
- [ ] Implement Geist font loading
- [ ] Set up Motion animation library
- [ ] Create base theme provider

### Week 3-4: Core Components
- [ ] Migrate button variants system
- [ ] Implement custom status icons
- [ ] Create priority icon system
- [ ] Build responsive layout components

### Week 5-6: Interactive Features
- [ ] Add drag & drop functionality
- [ ] Implement context menus
- [ ] Enhance search components
- [ ] Add keyboard shortcuts

### Week 7-8: Integration & Polish
- [ ] Integrate with team management features
- [ ] Add accessibility enhancements
- [ ] Performance optimization
- [ ] Cross-browser testing

---

*This comprehensive UI migration plan ensures the sophisticated visual design and interaction patterns from Circle-temp are preserved while enhancing the team management system with modern, polished user experience patterns.*
