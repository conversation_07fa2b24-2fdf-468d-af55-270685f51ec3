{"name": "@thf/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build:styles": "tailwindcss -i ./src/styles.css -o ./dist/index.css", "build:components": "tsc", "check-types": "tsc --noEmit", "dev:styles": "tailwindcss -i ./src/styles.css -o ./dist/index.css --watch", "dev:components": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./styles.css": "./src/styles.css", "./components/*": "./src/components/*.tsx", "./utils/*": "./src/utils/*.ts", "./icons/*": "./src/icons/*.tsx", "./svgs/*": "./src/svgs/*.tsx", "./animations/*": "./src/animations/*.tsx"}, "dependencies": {"react": "^19", "@thf/supabase": "*", "@thf/store": "*", "@thf/sanity": "*", "@thf/constants": "*", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "@tanstack/react-table": "^8.21.3", "@hookform/resolvers": "^4.1.3", "next-cloudinary": "^6.16.0", "@motionone/utils": "^10.18.0", "class-variance-authority": "^0.7.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "sharp": "^0.33.5", "sonner": "^2.0.1", "vaul": "^1.1.2", "zod": "^3.24.2", "react-hook-form": "^7.54.2", "react-use-measure": "^2.1.7", "recharts": "^2.15.1", "date-fns": "^4.1.0", "lucide-react": "^0.483.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/tailwind-config": "*", "@thf/typescript-config": "*", "@tailwindcss/cli": "^4.1.5", "@types/react": "^19.1.0", "eslint": "^9.28.0", "tailwindcss": "^4.1.5", "typescript": "5.8.2"}}