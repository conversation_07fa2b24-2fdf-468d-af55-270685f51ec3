
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import { z } from 'zod';

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const contactFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().min(10, { message: 'Please enter a valid phone number.' }),
  reason: z.string().min(1, { message: 'Please select a reason for contact.' }),
  message: z
    .string()
    .min(10, { message: 'Please enter a message (at least 10 characters).' }),
});

type wd = z.infer<typeof contactFormSchema>;

export const ContactClient = ({ name }: wd) => (
  <Html>
    <Head />
    {/* <Preview>You're on thehuefactory waitlist!</Preview> */}
    <Body style={main}>
      <Container
        style={{
          ...container,
          backgroundColor: colors['100'],
        }}
      >
        <Img
          src={`${baseUrl}/email_hero.jpg`}
          width='100%'
          height='auto'
          alt='Email Header Image'
        />
      </Container>
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['100'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            // marginTop: '-4rem',
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        ></Section>
      </Container>
      <Container
        style={{
          ...container,
          alignItems: 'center',
          alignContent: 'center',
        }}
      >
        <Heading style={{ ...h1, marginBottom: '48px', marginTop: '48px' }}>
          Hi {name},
        </Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Your Contact message has been received.
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          We&apos;ll be in touch as soon as soon as possible.
        </Text>
      </Container>
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
          height='42'
          alt="thehuefactory's Logo"
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            target='_blank'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2025 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

export const ContactAdmin = ({ name, email, message, phone, reason }: wd) => (
  <Html>
    <Head />
    {/* <Preview>You're on thehuefactory waitlist!</Preview> */}
    <Body style={main}>
      <Container
        style={{
          ...container,
          backgroundColor: colors['100'],
        }}
      >
        <Img
          src={`${baseUrl}/thehuefactory_hero.png`}
          width='100%'
          height='auto'
          alt='Email Header Image'
        />
      </Container>
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['100'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            // marginTop: '-4rem',
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        ></Section>
      </Container>
      <Container
        style={{
          ...container,
          alignItems: 'center',
          alignContent: 'center',
        }}
      >
        <Heading style={{ ...h1, marginBottom: '48px', marginTop: '48px' }}>
          A {reason} message from {name},
        </Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Name : {name} <br />
          Email Address : {email} <br />
          Phone Number : {phone} <br />
          reason : {reason} <br />
          message: {message} <br />
        </Text>
      </Container>
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
          height='42'
          alt="thehuefactory's Logo"
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            target='_blank'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2025 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '20px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const code = {
  display: 'inline-block',
  padding: '16px 4.5%',
  width: '90.5%',
  backgroundColor: '#f4f4f4',
  borderRadius: '5px',
  border: '1px solid #eee',
  color: '#333',
};
