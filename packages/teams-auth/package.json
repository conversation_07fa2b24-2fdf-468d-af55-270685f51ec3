{"name": "@thf/teams-auth", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./hooks/*": "./src/hooks/*.ts", "./providers/*": "./src/providers/*.tsx", "./middleware/*": "./src/middleware/*.ts"}, "dependencies": {"@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.50.0", "@thf/supabase": "*", "next": "^15.3.0", "react": "^19.1.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^19.1.8", "eslint": "^9.29.0", "typescript": "5.8.2"}}