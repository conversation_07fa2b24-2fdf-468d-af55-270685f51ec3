{"name": "@thf/teams-auth", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./hooks/*": "./src/hooks/*.ts", "./providers/*": "./src/providers/*.tsx", "./middleware/*": "./src/middleware/*.ts"}, "dependencies": {"react": "^19.1.0", "@thf/supabase": "*", "@supabase/ssr": "^0.5.1", "next": "^15.3.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^19.1.0", "eslint": "^9.28.0", "typescript": "5.8.2"}}