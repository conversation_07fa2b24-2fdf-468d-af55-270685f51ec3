import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Authentication Middleware
 * Handles route protection and authentication for the teams application
 */

interface AuthMiddlewareConfig {
  publicRoutes?: string[];
  protectedRoutes?: string[];
  adminRoutes?: string[];
  redirectTo?: string;
  loginPath?: string;
}

const defaultConfig: AuthMiddlewareConfig = {
  publicRoutes: ['/login', '/signup', '/forgot-password'],
  protectedRoutes: ['/dashboard', '/applications', '/members', '/projects', '/proposals', '/nda'],
  adminRoutes: ['/settings', '/admin'],
  redirectTo: '/dashboard',
  loginPath: '/login',
};

/**
 * Create authentication middleware
 */
export function createAuthMiddleware(config: AuthMiddlewareConfig = {}) {
  const finalConfig = { ...defaultConfig, ...config };

  return async function authMiddleware(request: NextRequest) {
    const response = NextResponse.next();
    const pathname = request.nextUrl.pathname;

    // Create Supabase client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set(name: string, value: string, options: any) {
            request.cookies.set({ name, value, ...options });
            response.cookies.set({ name, value, ...options });
          },
          remove(name: string, options: any) {
            request.cookies.set({ name, value: '', ...options });
            response.cookies.set({ name, value: '', ...options });
          },
        },
      }
    );

    // Get session
    const { data: { session } } = await supabase.auth.getSession();

    // Check if route is public
    const isPublicRoute = finalConfig.publicRoutes?.some(route => 
      pathname.startsWith(route)
    );

    // Check if route is protected
    const isProtectedRoute = finalConfig.protectedRoutes?.some(route => 
      pathname.startsWith(route)
    );

    // Check if route is admin-only
    const isAdminRoute = finalConfig.adminRoutes?.some(route => 
      pathname.startsWith(route)
    );

    // If user is not authenticated and trying to access protected route
    if (!session && isProtectedRoute) {
      const loginUrl = new URL(finalConfig.loginPath!, request.url);
      loginUrl.searchParams.set('redirectTo', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // If user is authenticated and trying to access public route (like login)
    if (session && isPublicRoute && pathname !== '/') {
      return NextResponse.redirect(new URL(finalConfig.redirectTo!, request.url));
    }

    // Check admin permissions for admin routes
    if (session && isAdminRoute) {
      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('user_id', session.user.id)
          .single();

        if (!profile || profile.role !== 'Admin') {
          return NextResponse.redirect(new URL('/unauthorized', request.url));
        }
      } catch (error) {
        console.error('Error checking admin permissions:', error);
        return NextResponse.redirect(new URL('/error', request.url));
      }
    }

    return response;
  };
}

/**
 * Route protection utilities
 */
export const routeUtils = {
  /**
   * Check if a route is public
   */
  isPublicRoute: (pathname: string, publicRoutes: string[] = defaultConfig.publicRoutes!) => {
    return publicRoutes.some(route => pathname.startsWith(route));
  },

  /**
   * Check if a route is protected
   */
  isProtectedRoute: (pathname: string, protectedRoutes: string[] = defaultConfig.protectedRoutes!) => {
    return protectedRoutes.some(route => pathname.startsWith(route));
  },

  /**
   * Check if a route is admin-only
   */
  isAdminRoute: (pathname: string, adminRoutes: string[] = defaultConfig.adminRoutes!) => {
    return adminRoutes.some(route => pathname.startsWith(route));
  },

  /**
   * Get redirect URL with return path
   */
  getRedirectUrl: (loginPath: string, returnPath: string, baseUrl: string) => {
    const url = new URL(loginPath, baseUrl);
    url.searchParams.set('redirectTo', returnPath);
    return url.toString();
  },
};

/**
 * Server-side authentication utilities
 */
export const serverAuth = {
  /**
   * Get server-side session
   */
  getSession: async (request: NextRequest) => {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set() {},
          remove() {},
        },
      }
    );

    const { data: { session } } = await supabase.auth.getSession();
    return session;
  },

  /**
   * Get server-side user profile
   */
  getProfile: async (request: NextRequest) => {
    const session = await serverAuth.getSession(request);
    if (!session) return null;

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set() {},
          remove() {},
        },
      }
    );

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', session.user.id)
      .single();

    return profile;
  },

  /**
   * Check server-side permissions
   */
  hasPermission: async (request: NextRequest, permission: string) => {
    const profile = await serverAuth.getProfile(request);
    if (!profile) return false;

    // Role permissions mapping (same as client-side)
    const rolePermissions: Record<string, string[]> = {
      Admin: [
        'applications.read', 'applications.write', 'applications.approve', 'applications.reject',
        'members.read', 'members.write', 'members.delete',
        'projects.read', 'projects.write', 'projects.delete',
        'proposals.read', 'proposals.write', 'proposals.approve',
        'settings.read', 'settings.write',
      ],
      Collaborator: [
        'applications.read', 'members.read', 'projects.read', 'projects.write',
        'proposals.read', 'proposals.write',
      ],
      Affiliate: ['proposals.read', 'proposals.write', 'projects.read'],
      Volunteer: ['projects.read'],
    };

    const permissions = rolePermissions[profile.role] || [];
    return permissions.includes(permission);
  },
};
