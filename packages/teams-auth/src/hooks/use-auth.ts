import { useTeamsAuth } from '../providers/auth-provider';

/**
 * Authentication Hooks
 * Convenient hooks for common authentication patterns
 */

/**
 * Hook for authentication state
 */
export const useAuth = () => {
  const auth = useTeamsAuth();
  
  return {
    ...auth,
    isAuthenticated: !!auth.user,
    isLoading: auth.loading,
  };
};

/**
 * Hook for user profile management
 */
export const useProfile = () => {
  const { profile, updateProfile, loading } = useTeamsAuth();
  
  return {
    profile,
    updateProfile,
    loading,
    hasProfile: !!profile,
  };
};

/**
 * Hook for permission checking
 */
export const usePermissions = () => {
  const { hasPermission, hasRole, isAdmin, profile } = useTeamsAuth();
  
  return {
    hasPermission,
    hasRole,
    isAdmin,
    role: profile?.role,
    permissions: {
      // Application permissions
      canViewApplications: hasPermission('applications.read'),
      canManageApplications: hasPermission('applications.write'),
      canApproveApplications: hasPermission('applications.approve'),
      canRejectApplications: hasPermission('applications.reject'),
      
      // Member permissions
      canViewMembers: hasPermission('members.read'),
      canManageMembers: hasPermission('members.write'),
      canDeleteMembers: hasPermission('members.delete'),
      
      // Project permissions
      canViewProjects: hasPermission('projects.read'),
      canManageProjects: hasPermission('projects.write'),
      canDeleteProjects: hasPermission('projects.delete'),
      
      // Proposal permissions
      canViewProposals: hasPermission('proposals.read'),
      canManageProposals: hasPermission('proposals.write'),
      canApproveProposals: hasPermission('proposals.approve'),
      
      // Settings permissions
      canViewSettings: hasPermission('settings.read'),
      canManageSettings: hasPermission('settings.write'),
    },
  };
};

/**
 * Hook for authentication actions
 */
export const useAuthActions = () => {
  const { signIn, signUp, signOut } = useTeamsAuth();
  
  return {
    signIn,
    signUp,
    signOut,
  };
};

/**
 * Hook for role-based UI rendering
 */
export const useRoleUI = () => {
  const { profile, hasRole } = useTeamsAuth();
  
  const showForRole = (role: string | string[]) => {
    if (!profile) return false;
    
    if (Array.isArray(role)) {
      return role.includes(profile.role);
    }
    
    return hasRole(role as any);
  };
  
  const showForAdmin = () => showForRole('Admin');
  const showForCollaborator = () => showForRole(['Admin', 'Collaborator']);
  const showForAffiliate = () => showForRole(['Admin', 'Collaborator', 'Affiliate']);
  const showForVolunteer = () => showForRole(['Admin', 'Collaborator', 'Affiliate', 'Volunteer']);
  
  return {
    showForRole,
    showForAdmin,
    showForCollaborator,
    showForAffiliate,
    showForVolunteer,
    currentRole: profile?.role,
  };
};

/**
 * Hook for NDA status
 */
export const useNDAStatus = () => {
  const { profile } = useTeamsAuth();
  
  return {
    isNdaSigned: profile?.is_nda_signed || false,
    ndaSignedDate: profile?.nda_signed_date,
    requiresNda: !profile?.is_nda_signed,
  };
};
