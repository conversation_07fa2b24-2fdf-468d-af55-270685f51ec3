'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { createClient } from '@thf/supabase/client';

/**
 * Teams Authentication Provider
 * Manages authentication state and user sessions for the teams application
 */

export interface TeamRole {
  id: string;
  name: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  permissions: string[];
}

export interface TeamProfile {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  role: TeamRole['name'];
  avatar_url?: string;
  is_nda_signed: boolean;
  nda_signed_date?: string;
  created_at: string;
  updated_at: string;
  skills?: string[];
  bio?: string;
  portfolio_url?: string;
}

interface TeamsAuthContextType {
  user: User | null;
  profile: TeamProfile | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: any }>;
  signOut: () => Promise<{ error: any }>;
  updateProfile: (updates: Partial<TeamProfile>) => Promise<{ error: any }>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: TeamRole['name']) => boolean;
  isAdmin: boolean;
}

const TeamsAuthContext = createContext<TeamsAuthContextType | undefined>(undefined);

interface TeamsAuthProviderProps {
  children: React.ReactNode;
}

export const TeamsAuthProvider: React.FC<TeamsAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<TeamProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  const supabase = createClient();

  // Role permissions mapping
  const rolePermissions: Record<TeamRole['name'], string[]> = {
    Admin: [
      'applications.read',
      'applications.write',
      'applications.approve',
      'applications.reject',
      'members.read',
      'members.write',
      'members.delete',
      'projects.read',
      'projects.write',
      'projects.delete',
      'proposals.read',
      'proposals.write',
      'proposals.approve',
      'settings.read',
      'settings.write',
    ],
    Collaborator: [
      'applications.read',
      'members.read',
      'projects.read',
      'projects.write',
      'proposals.read',
      'proposals.write',
    ],
    Affiliate: [
      'proposals.read',
      'proposals.write',
      'projects.read',
    ],
    Volunteer: [
      'projects.read',
    ],
  };

  // Fetch user profile
  const fetchProfile = async (userId: string): Promise<TeamProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }

      return data as TeamProfile;
    } catch (error) {
      console.error('Error fetching profile:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession();
        
        if (initialSession?.user) {
          setUser(initialSession.user);
          setSession(initialSession);
          
          const userProfile = await fetchProfile(initialSession.user.id);
          setProfile(userProfile);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          const userProfile = await fetchProfile(session.user.id);
          setProfile(userProfile);
        } else {
          setProfile(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // Authentication methods
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    return { error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const updateProfile = async (updates: Partial<TeamProfile>) => {
    if (!user) return { error: new Error('No user logged in') };

    const { error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('user_id', user.id);

    if (!error && profile) {
      setProfile({ ...profile, ...updates });
    }

    return { error };
  };

  // Permission checking
  const hasPermission = (permission: string): boolean => {
    if (!profile) return false;
    const permissions = rolePermissions[profile.role] || [];
    return permissions.includes(permission);
  };

  const hasRole = (role: TeamRole['name']): boolean => {
    return profile?.role === role;
  };

  const isAdmin = profile?.role === 'Admin';

  const value: TeamsAuthContextType = {
    user,
    profile,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    hasPermission,
    hasRole,
    isAdmin,
  };

  return (
    <TeamsAuthContext.Provider value={value}>
      {children}
    </TeamsAuthContext.Provider>
  );
};

/**
 * Hook to use teams authentication context
 */
export const useTeamsAuth = (): TeamsAuthContextType => {
  const context = useContext(TeamsAuthContext);
  if (context === undefined) {
    throw new Error('useTeamsAuth must be used within a TeamsAuthProvider');
  }
  return context;
};

/**
 * Higher-order component for protecting routes
 */
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: TeamRole['name'];
  requiredPermission?: string;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermission,
  fallback = <div>Access denied</div>,
}) => {
  const { user, profile, loading } = useTeamsAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user || !profile) {
    return fallback;
  }

  if (requiredRole && profile.role !== requiredRole) {
    return fallback;
  }

  if (requiredPermission) {
    const { hasPermission } = useTeamsAuth();
    if (!hasPermission(requiredPermission)) {
      return fallback;
    }
  }

  return <>{children}</>;
};

/**
 * Hook for role-based conditional rendering
 */
export const useRoleAccess = () => {
  const { profile, hasPermission, hasRole, isAdmin } = useTeamsAuth();

  return {
    profile,
    hasPermission,
    hasRole,
    isAdmin,
    canManageApplications: hasPermission('applications.write'),
    canApproveApplications: hasPermission('applications.approve'),
    canManageMembers: hasPermission('members.write'),
    canManageProjects: hasPermission('projects.write'),
    canManageSettings: hasPermission('settings.write'),
  };
};
