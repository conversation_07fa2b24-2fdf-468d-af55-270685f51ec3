{"name": "@thf/animations", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./layout": "./src/layout/index.tsx", "./micro": "./src/micro/index.tsx", "./transitions": "./src/transitions/index.tsx"}, "dependencies": {"react": "^18.3.0", "framer-motion": "^11.11.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^18.3.0", "eslint": "^9.28.0", "typescript": "5.8.2"}}