{"name": "@thf/icons", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./status": "./src/status/index.tsx", "./priority": "./src/priority/index.tsx", "./navigation": "./src/navigation/index.tsx"}, "dependencies": {"react": "^19.1.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^19.1.0", "eslint": "^9.28.0", "typescript": "5.8.2"}}