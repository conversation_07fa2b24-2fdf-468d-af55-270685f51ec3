import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
}

/**
 * Dashboard Icon
 */
export const DashboardIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <rect x="2" y="2" width="5" height="5" rx="1" />
    <rect x="9" y="2" width="5" height="3" rx="1" />
    <rect x="9" y="7" width="5" height="7" rx="1" />
    <rect x="2" y="9" width="5" height="5" rx="1" />
  </svg>
);

/**
 * Applications Icon
 */
export const ApplicationsIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M3 2h10a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1z" />
    <path d="M5 6h6" />
    <path d="M5 8h6" />
    <path d="M5 10h4" />
  </svg>
);

/**
 * Members Icon
 */
export const MembersIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" />
    <path d="M5 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" />
    <path d="M14 13s-1-2-3-2-3 2-3 2" />
    <path d="M8 13s-1-2-3-2-3 2-3 2" />
  </svg>
);

/**
 * Projects Icon
 */
export const ProjectsIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M2 3h12a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1z" />
    <path d="M2 7h14" />
    <path d="M6 3v10" />
  </svg>
);

/**
 * Proposals Icon
 */
export const ProposalsIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M3 2h8l2 2v8a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1z" />
    <path d="M11 2v2h2" />
    <path d="M5 8h4" />
    <path d="M5 10h4" />
  </svg>
);

/**
 * NDA Icon
 */
export const NDAIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M4 2h8a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1z" />
    <path d="M6 6h4" />
    <path d="M6 8h4" />
    <path d="M6 10h2" />
    <circle cx="10" cy="10" r="1" fill="currentColor" />
  </svg>
);

/**
 * Settings Icon
 */
export const SettingsIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <circle cx="8" cy="8" r="3" />
    <path d="M8 1v2" />
    <path d="M8 13v2" />
    <path d="M15 8h-2" />
    <path d="M3 8H1" />
    <path d="M12.95 4.95l-1.41 1.41" />
    <path d="M4.46 10.54l-1.41 1.41" />
    <path d="M12.95 11.05l-1.41-1.41" />
    <path d="M4.46 5.46l-1.41-1.41" />
  </svg>
);

/**
 * Inbox Icon
 */
export const InboxIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M2 4h12l-1 8H3L2 4z" />
    <path d="M2 4l1-2h10l1 2" />
    <path d="M6 8h4" />
  </svg>
);

/**
 * Teams Icon
 */
export const TeamsIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M8 7a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" />
    <path d="M14 15s-1-3-6-3-6 3-6 3" />
  </svg>
);

/**
 * Search Icon
 */
export const SearchIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <circle cx="7" cy="7" r="5" />
    <path d="M13 13l-2.5-2.5" />
  </svg>
);

/**
 * Plus Icon (for creating new items)
 */
export const PlusIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M8 2v12" />
    <path d="M2 8h12" />
  </svg>
);

/**
 * Filter Icon
 */
export const FilterIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M2 3h12l-5 6v4l-2-1V9L2 3z" />
  </svg>
);

/**
 * Sort Icon
 */
export const SortIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M3 6l3-3 3 3" />
    <path d="M6 3v10" />
    <path d="M13 10l-3 3-3-3" />
    <path d="M10 13V3" />
  </svg>
);

/**
 * View Grid Icon
 */
export const ViewGridIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <rect x="2" y="2" width="5" height="5" rx="1" />
    <rect x="9" y="2" width="5" height="5" rx="1" />
    <rect x="2" y="9" width="5" height="5" rx="1" />
    <rect x="9" y="9" width="5" height="5" rx="1" />
  </svg>
);

/**
 * View List Icon
 */
export const ViewListIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <path d="M2 4h12" />
    <path d="M2 8h12" />
    <path d="M2 12h12" />
  </svg>
);

// Navigation configuration
export interface NavigationItem {
  id: string;
  name: string;
  icon: React.FC<IconProps>;
  href: string;
  badge?: number;
}

export const mainNavigation: NavigationItem[] = [
  { id: 'dashboard', name: 'Dashboard', icon: DashboardIcon, href: '/dashboard' },
  { id: 'applications', name: 'Applications', icon: ApplicationsIcon, href: '/applications' },
  { id: 'members', name: 'Members', icon: MembersIcon, href: '/members' },
  { id: 'projects', name: 'Projects', icon: ProjectsIcon, href: '/projects' },
  { id: 'proposals', name: 'Proposals', icon: ProposalsIcon, href: '/proposals' },
  { id: 'nda', name: 'NDA', icon: NDAIcon, href: '/nda' },
];

export const utilityNavigation: NavigationItem[] = [
  { id: 'inbox', name: 'Inbox', icon: InboxIcon, href: '/inbox' },
  { id: 'search', name: 'Search', icon: SearchIcon, href: '/search' },
  { id: 'settings', name: 'Settings', icon: SettingsIcon, href: '/settings' },
];

/**
 * Navigation Icon Component with dynamic icon
 */
export const NavigationIcon: React.FC<{ iconId: string } & IconProps> = ({ 
  iconId, 
  ...props 
}) => {
  const allItems = [...mainNavigation, ...utilityNavigation];
  const item = allItems.find((item) => item.id === iconId);
  
  if (!item) return null;

  const IconComponent = item.icon;
  return <IconComponent {...props} />;
};
