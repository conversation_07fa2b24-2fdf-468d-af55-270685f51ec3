import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
}

/**
 * Backlog Status Icon
 * Dashed circle with empty center
 */
export const BacklogIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Backlog Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#bec2c8"
      strokeWidth="2"
      strokeDasharray="1.4 1.74"
      strokeDashoffset="0.65"
    />
    <circle
      className="progress"
      cx="7"
      cy="7"
      r="2"
      fill="none"
      stroke="#bec2c8"
      strokeWidth="4"
      strokeDasharray="0 100"
      strokeDashoffset="0"
      transform="rotate(-90 7 7)"
    />
  </svg>
);

/**
 * Paused Status Icon
 * Blue circle with partial progress
 */
export const PausedIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Paused Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#0ea5e9"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <circle
      className="progress"
      cx="7"
      cy="7"
      r="2"
      fill="none"
      stroke="#0ea5e9"
      strokeWidth="4"
      strokeDasharray="6.2517693806436885 100"
      strokeDashoffset="0"
      transform="rotate(-90 7 7)"
    />
  </svg>
);

/**
 * To Do Status Icon
 * Gray circle with no progress
 */
export const ToDoIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="To Do Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#e2e2e2"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <circle
      className="progress"
      cx="7"
      cy="7"
      r="2"
      fill="none"
      stroke="#e2e2e2"
      strokeWidth="4"
      strokeDasharray="0 100"
      strokeDashoffset="0"
      transform="rotate(-90 7 7)"
    />
  </svg>
);

/**
 * In Progress Status Icon
 * Yellow circle with partial progress
 */
export const InProgressIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="In Progress Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#facc15"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <circle
      className="progress"
      cx="7"
      cy="7"
      r="2"
      fill="none"
      stroke="#facc15"
      strokeWidth="4"
      strokeDasharray="2.0839231268812295 100"
      strokeDashoffset="0"
      transform="rotate(-90 7 7)"
    />
  </svg>
);

/**
 * Technical Review Status Icon
 * Green circle with more progress
 */
export const TechnicalReviewIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Technical Review Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#22c55e"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <circle
      className="progress"
      cx="7"
      cy="7"
      r="2"
      fill="none"
      stroke="#22c55e"
      strokeWidth="4"
      strokeDasharray="4.167846253762459 100"
      strokeDashoffset="0"
      transform="rotate(-90 7 7)"
    />
  </svg>
);

/**
 * Completed Status Icon
 * Purple circle with checkmark
 */
export const CompletedIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Completed Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#8b5cf6"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <path
      d="M4.5 7L6.5 9L9.5 5"
      stroke="#8b5cf6"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

/**
 * Team Application Status Icons
 * Adapted for team management workflow
 */

/**
 * Received Status Icon (for applications)
 */
export const ReceivedIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Received Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#6b7280"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <circle
      className="progress"
      cx="7"
      cy="7"
      r="2"
      fill="none"
      stroke="#6b7280"
      strokeWidth="4"
      strokeDasharray="1 100"
      strokeDashoffset="0"
      transform="rotate(-90 7 7)"
    />
  </svg>
);

/**
 * Reviewed Status Icon (for applications)
 */
export const ReviewedIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Reviewed Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#3b82f6"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <circle
      className="progress"
      cx="7"
      cy="7"
      r="2"
      fill="none"
      stroke="#3b82f6"
      strokeWidth="4"
      strokeDasharray="3 100"
      strokeDashoffset="0"
      transform="rotate(-90 7 7)"
    />
  </svg>
);

/**
 * Accepted Status Icon (for applications)
 */
export const AcceptedIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Accepted Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#10b981"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <path
      d="M4.5 7L6.5 9L9.5 5"
      stroke="#10b981"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

/**
 * Rejected Status Icon (for applications)
 */
export const RejectedIcon = ({ className, ...props }: IconProps) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    className={className}
    aria-label="Rejected Status"
    role="img"
    focusable="false"
    {...props}
  >
    <circle
      cx="7"
      cy="7"
      r="6"
      fill="none"
      stroke="#ef4444"
      strokeWidth="2"
      strokeDasharray="3.14 0"
      strokeDashoffset="-0.7"
    />
    <path
      d="M5 5L9 9M9 5L5 9"
      stroke="#ef4444"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Status configuration
export interface Status {
  id: string;
  name: string;
  color: string;
  icon: React.FC<IconProps>;
}

export const projectStatuses: Status[] = [
  { id: 'backlog', name: 'Backlog', color: '#ec4899', icon: BacklogIcon },
  { id: 'to-do', name: 'Todo', color: '#f97316', icon: ToDoIcon },
  { id: 'in-progress', name: 'In Progress', color: '#facc15', icon: InProgressIcon },
  { id: 'technical-review', name: 'Technical Review', color: '#22c55e', icon: TechnicalReviewIcon },
  { id: 'completed', name: 'Completed', color: '#8b5cf6', icon: CompletedIcon },
  { id: 'paused', name: 'Paused', color: '#0ea5e9', icon: PausedIcon },
];

export const applicationStatuses: Status[] = [
  { id: 'received', name: 'Received', color: '#6b7280', icon: ReceivedIcon },
  { id: 'reviewed', name: 'Reviewed', color: '#3b82f6', icon: ReviewedIcon },
  { id: 'accepted', name: 'Accepted', color: '#10b981', icon: AcceptedIcon },
  { id: 'rejected', name: 'Rejected', color: '#ef4444', icon: RejectedIcon },
];

/**
 * Status Icon Component with dynamic status
 */
export const StatusIcon: React.FC<{ statusId: string; type?: 'project' | 'application' } & IconProps> = ({ 
  statusId, 
  type = 'project',
  ...props 
}) => {
  const statuses = type === 'application' ? applicationStatuses : projectStatuses;
  const currentStatus = statuses.find((s) => s.id === statusId);
  
  if (!currentStatus) return null;

  const IconComponent = currentStatus.icon;
  return <IconComponent {...props} />;
};
