{"name": "@thf/theme", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./colors": "./src/colors.ts", "./fonts": "./src/fonts.ts", "./theme-provider": "./src/theme-provider.tsx", "./globals.css": "./src/globals.css"}, "dependencies": {"react": "^18.3.0", "next": "^15.1.0", "next-themes": "^0.4.4", "lucide-react": "^0.446.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^18.3.0", "eslint": "^9.28.0", "typescript": "5.8.2"}}