{"name": "@thf/theme", "version": "0.0.0", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./colors": "./src/colors.ts", "./fonts": "./src/fonts.ts", "./theme-provider": "./src/theme-provider.tsx", "./globals.css": "./src/globals.css"}, "dependencies": {"react": "^19.1.0", "next": "^15.3.4", "next-themes": "^0.4.6", "lucide-react": "^0.519.0"}, "devDependencies": {"@types/react": "^19.1.8", "eslint": "^9.29.0", "typescript": "^5.8.3"}}