/**
 * Font configuration for Teams Application
 * Using Geist Sans and <PERSON>eist Mono from Circle-temp
 */

import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';

export const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
});

export const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
});

export const fontVariables = `${geistSans.variable} ${geistMono.variable}`;

// Font utility classes
export const fontClasses = {
  sans: 'font-sans',
  mono: 'font-mono',
  
  // Text sizes with responsive variants
  textXs: 'text-xs',
  textSm: 'text-sm',
  textBase: 'text-base',
  textLg: 'text-lg',
  textXl: 'text-xl',
  text2Xl: 'text-2xl',
  
  // Responsive text patterns from Circle-temp
  textResponsive: 'text-xs sm:text-sm',
  textResponsiveMedium: 'text-xs sm:text-sm font-medium sm:font-semibold',
  
  // Font weights
  fontNormal: 'font-normal',
  fontMedium: 'font-medium',
  fontSemibold: 'font-semibold',
  fontBold: 'font-bold',
  
  // Line heights
  leadingTight: 'leading-tight',
  leadingNormal: 'leading-normal',
  leadingRelaxed: 'leading-relaxed',
  
  // Text colors
  textMuted: 'text-muted-foreground',
  textPrimary: 'text-foreground',
  textSecondary: 'text-secondary-foreground',
} as const;

export type FontClass = keyof typeof fontClasses;
