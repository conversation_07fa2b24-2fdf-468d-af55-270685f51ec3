/**
 * OKLCH Color System for Teams Application
 * Based on Circle-temp's sophisticated color palette
 */

export const oklchColors = {
  light: {
    // Base colors
    background: 'oklch(1 0 0)',
    foreground: 'oklch(0.141 0.005 285.823)',
    
    // Card colors
    card: 'oklch(1 0 0)',
    cardForeground: 'oklch(0.141 0.005 285.823)',
    
    // Popover colors
    popover: 'oklch(1 0 0)',
    popoverForeground: 'oklch(0.141 0.005 285.823)',
    
    // Primary colors
    primary: 'oklch(0.21 0.006 285.885)',
    primaryForeground: 'oklch(0.985 0 0)',
    
    // Secondary colors
    secondary: 'oklch(0.967 0.001 286.375)',
    secondaryForeground: 'oklch(0.21 0.006 285.885)',
    
    // Muted colors
    muted: 'oklch(0.967 0.001 286.375)',
    mutedForeground: 'oklch(0.552 0.016 285.938)',
    
    // Accent colors
    accent: 'oklch(0.967 0.001 286.375)',
    accentForeground: 'oklch(0.21 0.006 285.885)',
    
    // Destructive colors
    destructive: 'oklch(0.577 0.245 27.325)',
    destructiveForeground: 'oklch(0.577 0.245 27.325)',
    
    // Border and input colors
    border: 'oklch(0.92 0.004 286.32)',
    input: 'oklch(0.92 0.004 286.32)',
    ring: 'oklch(0.871 0.006 286.286)',
    
    // Chart colors
    chart1: 'oklch(0.646 0.222 41.116)',
    chart2: 'oklch(0.6 0.118 184.704)',
    chart3: 'oklch(0.398 0.07 227.392)',
    chart4: 'oklch(0.828 0.189 84.429)',
    chart5: 'oklch(0.769 0.188 70.08)',
    
    // Sidebar colors
    sidebar: 'oklch(0.985 0 0)',
    sidebarForeground: 'oklch(0.141 0.005 285.823)',
    sidebarPrimary: 'oklch(0.21 0.006 285.885)',
    sidebarPrimaryForeground: 'oklch(0.985 0 0)',
    sidebarAccent: 'oklch(0.967 0.001 286.375)',
    sidebarAccentForeground: 'oklch(0.21 0.006 285.885)',
    sidebarBorder: 'oklch(0.92 0.004 286.32)',
    sidebarRing: 'oklch(0.871 0.006 286.286)',
    
    // Container color
    container: '#fff',
  },
  
  dark: {
    // Base colors
    background: 'oklch(0.141 0.005 285.823)',
    foreground: 'oklch(0.985 0 0)',
    
    // Card colors
    card: 'oklch(0.141 0.005 285.823)',
    cardForeground: 'oklch(0.985 0 0)',
    
    // Popover colors
    popover: 'oklch(0.141 0.005 285.823)',
    popoverForeground: 'oklch(0.985 0 0)',
    
    // Primary colors
    primary: 'oklch(0.985 0 0)',
    primaryForeground: 'oklch(0.21 0.006 285.885)',
    
    // Secondary colors
    secondary: 'oklch(0.274 0.006 286.033)',
    secondaryForeground: 'oklch(0.985 0 0)',
    
    // Muted colors
    muted: 'oklch(0.274 0.006 286.033)',
    mutedForeground: 'oklch(0.705 0.015 286.067)',
    
    // Accent colors
    accent: 'oklch(0.274 0.006 286.033)',
    accentForeground: 'oklch(0.985 0 0)',
    
    // Destructive colors
    destructive: 'oklch(0.396 0.141 25.723)',
    destructiveForeground: 'oklch(0.637 0.237 25.331)',
    
    // Border and input colors
    border: 'oklch(0.274 0.006 286.033)',
    input: 'oklch(0.274 0.006 286.033)',
    ring: 'oklch(0.442 0.017 285.786)',
    
    // Chart colors
    chart1: 'oklch(0.488 0.243 264.376)',
    chart2: 'oklch(0.696 0.17 162.48)',
    chart3: 'oklch(0.769 0.188 70.08)',
    chart4: 'oklch(0.627 0.265 303.9)',
    chart5: 'oklch(0.645 0.246 16.439)',
    
    // Sidebar colors
    sidebar: 'oklch(0.21 0.006 285.885)',
    sidebarForeground: 'oklch(0.985 0 0)',
    sidebarPrimary: 'oklch(0.488 0.243 264.376)',
    sidebarPrimaryForeground: 'oklch(0.985 0 0)',
    sidebarAccent: 'oklch(0.274 0.006 286.033)',
    sidebarAccentForeground: 'oklch(0.985 0 0)',
    sidebarBorder: 'oklch(0.274 0.006 286.033)',
    sidebarRing: 'oklch(0.442 0.017 285.786)',
    
    // Container color
    container: '#101011',
  },
};

// Status colors (consistent across themes)
export const statusColors = {
  inProgress: '#facc15',
  technicalReview: '#22c55e',
  completed: '#8b5cf6',
  paused: '#0ea5e9',
  todo: '#f97316',
  backlog: '#ec4899',
  received: '#6b7280',
  reviewed: '#3b82f6',
  accepted: '#10b981',
  rejected: '#ef4444',
};

// Priority colors
export const priorityColors = {
  urgent: '#ef4444',
  high: '#f97316',
  medium: '#facc15',
  low: '#6b7280',
  noPriority: '#9ca3af',
};

// Role colors
export const roleColors = {
  admin: '#8b5cf6',
  collaborator: '#3b82f6',
  affiliate: '#10b981',
  volunteer: '#f59e0b',
};

export type ColorTheme = 'light' | 'dark';
export type ColorKey = keyof typeof oklchColors.light;
