import groq from 'groq';

const projectsFields = groq`
  _id,
  title,
  "slug": slug.current,
  subtitle,
  intro,
  projectlink,
  ogImage,
  carouselLandscape,
  carouselPortrait,
  coverPortraitImage,
  allProjectsLandscape,
  coverImage,
  row_one,
  row_two,
  row_three_first_column,
  row_three_second_column,
  row_four,
  row_five_first_column,
  row_five_second_column_one,
  row_five_second_column_two,
  row_six,
  publishedAt,
  "body": body[]{
    ...,
    _type == 'image' => {
      ...,
      "dimensions": asset->metadata.dimensions
    },
  }
`;

const projectsWCFields = groq`
  _id,
  projectype,
  title,
  "slug": slug.current,
  subtitle,
  publishedAt,
`;

// brand projects
export const projectsQuerry = groq`
*[_type == "brandprojects"] | order(publishedAt desc){
  ${projectsFields}
}`;
export const projectsWCQuerry = groq`
*[_type == "brandprojects"] | order(publishedAt desc){
  ${projectsWCFields}
}`;

export const projectQuerry = (slug: string) => groq`
*[_type == "brandprojects" && slug.current == "${slug}"][0]{
 ${projectsFields}
}`;
