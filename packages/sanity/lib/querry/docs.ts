import groq from 'groq';

const docsFields = groq`
  _id,
  title,
  "slug": slug.current,
  subtitle,
  publishedAt,
  "body": body[]{
    ...,
    _type == 'image' => {
      ...,
      "dimensions": asset->metadata.dimensions
    },
  }
`;

const docsWCFields = groq`
  _id,
  title,
  "slug": slug.current,
`;

// brand projects
export const docsQuerry = groq`
*[_type == "docs"] | order(publishedAt desc){
  ${docsFields}
}`;
export const docsWCQuerry = groq`
*[_type == "docs"] | order(publishedAt desc){
  ${docsWCFields}
}`;

export const docQuerry = (slug: string) => groq`
*[_type == "docs" && slug.current == "${slug}"][0]{
 ${docsFields}
}`;
