import { sanityClient } from './client';
import { brandproject, brandprojectwc, docs } from './types';
import { projectQuerry, projectsQuerry, projectsWCQuerry } from './querry';
import { docQuerry, docsQuerry, docsWCQuerry } from './querry/docs';

export async function getBrandProjects(): Promise<brandproject[]> {
  const res = await sanityClient.fetch(projectsQuerry);
  return res;
}
export async function getBrandProject(slug: string): Promise<brandproject> {
  const res = await sanityClient.fetch(projectQuerry(slug));
  return res;
}
export async function getBrandProjectsWC(): Promise<brandprojectwc> {
  const res = await sanityClient.fetch(projectsWCQuerry);
  return res;
}

export async function getDocs(): Promise<docs[]> {
  const res = await sanityClient.fetch(docsQuerry);
  return res;
}
export async function getDoc(slug: string): Promise<docs> {
  const res = await sanityClient.fetch(docQuerry(slug));
  return res;
}
export async function getDocsWC(): Promise<brandprojectwc> {
  const res = await sanityClient.fetch(docsWCQuerry);
  return res;
}
