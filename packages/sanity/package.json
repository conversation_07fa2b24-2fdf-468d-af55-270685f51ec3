{"name": "@thf/sanity", "version": "0.0.0", "type": "module", "private": true, "exports": {"./lib": "./lib/index.ts", "./schemas": "./schemas/index.ts", "./env": "./env.ts", "./structure": "./structure.ts"}, "devDependencies": {"groq": "^3.80.1", "next-sanity": "^9.9.5", "sanity": "^3.80.1", "sanity-plugin-cloudinary": "^1.2.0", "sharp": "^0.33.5", "styled-components": "^6.1.16", "@thf/eslint-config": "*", "@thf/typescript-config": "*", "eslint": "^9.28.0", "typescript": "5.8.2"}}