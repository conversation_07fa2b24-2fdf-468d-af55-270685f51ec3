{"name": "@thf/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.29.0", "eslint-config-next": "^15.3.4", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.4", "globals": "^16.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}}