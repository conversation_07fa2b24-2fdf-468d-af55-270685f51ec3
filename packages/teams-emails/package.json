{"name": "@thf/teams-emails", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./templates/*": "./src/templates/*.tsx", "./components/*": "./src/components/*.tsx"}, "dependencies": {"react": "^19.1.0", "@thf/emails": "*", "@react-email/components": "^0.0.25", "resend": "^4.0.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^19.1.0", "eslint": "^9.28.0", "typescript": "5.8.2"}}