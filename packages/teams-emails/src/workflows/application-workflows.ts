import React from 'react';
import {
  emailQueue,
  emailTemplates,
  emailUtils,
  sendEmail,
} from '../services/email-service';
import { ApplicationApprovedEmail } from '../templates/application-approved';
import { ApplicationReceivedEmail } from '../templates/application-received';
import { ApplicationRejectedEmail } from '../templates/application-rejected';
// Local type definition
interface TeamApplication {
  id: string;
  full_name: string;
  email: string;
  role: string;
  status: string;
  identifier: string;
  created_at: string;
  updated_at: string;
  reviewed_by?: string;
  notes?: string;
  is_nda_signed?: boolean;
  priority?: string;
}

/**
 * Application Email Workflows
 * Automated email workflows for application status changes
 */

/**
 * Send application received email
 */
export const sendApplicationReceivedEmail = async (
  application: TeamApplication
): Promise<void> => {
  const subject = emailUtils.generateSubject(
    emailTemplates.APPLICATION_RECEIVED,
    application.identifier
  );

  const template = React.createElement(ApplicationReceivedEmail, {
    applicantName: application.full_name,
    applicationId: application.identifier,
    role: application.role,
    submittedAt: application.created_at,
    dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/application/${application.identifier}`,
  });

  await sendEmail({
    to: application.email,
    subject,
    template,
    templateName: emailTemplates.APPLICATION_RECEIVED,
    recipientName: application.full_name,
    metadata: {
      applicationId: application.id,
      applicationIdentifier: application.identifier,
      role: application.role,
    },
  });
};

/**
 * Send application approved email
 */
export const sendApplicationApprovedEmail = async (
  application: TeamApplication,
  options?: {
    nextSteps?: string[];
    onboardingUrl?: string;
    ndaUrl?: string;
  }
): Promise<void> => {
  const subject = emailUtils.generateSubject(
    emailTemplates.APPLICATION_APPROVED,
    application.identifier
  );

  const template = React.createElement(ApplicationApprovedEmail, {
    applicantName: application.full_name,
    applicationId: application.identifier,
    role: application.role,
    approvedAt: application.updated_at,
    nextSteps: options?.nextSteps,
    onboardingUrl:
      options?.onboardingUrl || `${process.env.NEXT_PUBLIC_APP_URL}/onboarding`,
    ndaUrl: options?.ndaUrl || `${process.env.NEXT_PUBLIC_APP_URL}/nda`,
  });

  await sendEmail({
    to: application.email,
    subject,
    template,
    templateName: emailTemplates.APPLICATION_APPROVED,
    recipientName: application.full_name,
    metadata: {
      applicationId: application.id,
      applicationIdentifier: application.identifier,
      role: application.role,
      approvedBy: application.reviewed_by,
    },
  });
};

/**
 * Send application rejected email
 */
export const sendApplicationRejectedEmail = async (
  application: TeamApplication,
  options?: {
    feedback?: string;
    reapplyUrl?: string;
    waitlistUrl?: string;
  }
): Promise<void> => {
  const subject = emailUtils.generateSubject(
    emailTemplates.APPLICATION_REJECTED,
    application.identifier
  );

  const template = React.createElement(ApplicationRejectedEmail, {
    applicantName: application.full_name,
    applicationId: application.identifier,
    role: application.role,
    rejectedAt: application.updated_at,
    feedback: options?.feedback || application.notes,
    reapplyUrl:
      options?.reapplyUrl || `${process.env.NEXT_PUBLIC_MAIN_SITE_URL}/join`,
    waitlistUrl:
      options?.waitlistUrl ||
      `${process.env.NEXT_PUBLIC_MAIN_SITE_URL}/waitlist`,
  });

  await sendEmail({
    to: application.email,
    subject,
    template,
    templateName: emailTemplates.APPLICATION_REJECTED,
    recipientName: application.full_name,
    metadata: {
      applicationId: application.id,
      applicationIdentifier: application.identifier,
      role: application.role,
      rejectedBy: application.reviewed_by,
      feedback: options?.feedback || application.notes,
    },
  });
};

/**
 * Send application under review email
 */
export const sendApplicationUnderReviewEmail = async (
  application: TeamApplication
): Promise<void> => {
  const subject = emailUtils.generateSubject(
    emailTemplates.APPLICATION_UNDER_REVIEW,
    application.identifier
  );

  // For now, we'll use a simple text-based template
  // This can be expanded to a full React template later
  const template = React.createElement('div', {}, [
    React.createElement('h1', { key: 'title' }, 'Application Under Review'),
    React.createElement(
      'p',
      { key: 'greeting' },
      `Hi ${application.full_name},`
    ),
    React.createElement(
      'p',
      { key: 'content' },
      `Your application for ${application.role} (${application.identifier}) is now under review. We'll update you soon!`
    ),
  ]);

  await sendEmail({
    to: application.email,
    subject,
    template,
    templateName: emailTemplates.APPLICATION_UNDER_REVIEW,
    recipientName: application.full_name,
    metadata: {
      applicationId: application.id,
      applicationIdentifier: application.identifier,
      role: application.role,
    },
  });
};

/**
 * Workflow orchestrator for application status changes
 */
export const handleApplicationStatusChange = async (
  application: TeamApplication,
  previousStatus?: string
): Promise<void> => {
  // Don't send emails for initial status (received) if it's a new application
  if (application.status === 'received' && !previousStatus) {
    await sendApplicationReceivedEmail(application);
    return;
  }

  // Handle status transitions
  switch (application.status) {
    case 'reviewed':
      if (previousStatus === 'received') {
        await sendApplicationUnderReviewEmail(application);
      }
      break;

    case 'accepted':
      await sendApplicationApprovedEmail(application);
      break;

    case 'rejected':
      await sendApplicationRejectedEmail(application);
      break;

    default:
      console.log(
        `No email workflow defined for status: ${application.status}`
      );
  }
};

/**
 * Batch email workflows
 */
export const batchEmailWorkflows = {
  /**
   * Send reminder emails for pending applications
   */
  sendPendingApplicationReminders: async (applications: TeamApplication[]) => {
    const pendingApplications = applications.filter(
      (app) =>
        app.status === 'received' &&
        new Date(app.created_at) <
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days old
    );

    for (const application of pendingApplications) {
      emailQueue.add({
        to: '<EMAIL>',
        subject: `Reminder: Review pending application ${application.identifier}`,
        template: React.createElement('div', {}, [
          React.createElement(
            'h1',
            { key: 'title' },
            'Pending Application Reminder'
          ),
          React.createElement(
            'p',
            { key: 'content' },
            `Application ${application.identifier} from ${application.full_name} has been pending for over 7 days.`
          ),
        ]),
        templateName: 'pending-application-reminder',
        metadata: {
          applicationId: application.id,
          daysPending: Math.floor(
            (Date.now() - new Date(application.created_at).getTime()) /
              (24 * 60 * 60 * 1000)
          ),
        },
      });
    }
  },

  /**
   * Send weekly application summary
   */
  sendWeeklyApplicationSummary: async (applications: TeamApplication[]) => {
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentApplications = applications.filter(
      (app) => new Date(app.created_at) >= weekAgo
    );

    const summary = {
      total: recentApplications.length,
      byRole: {} as Record<string, number>,
      byStatus: {} as Record<string, number>,
    };

    recentApplications.forEach((app) => {
      summary.byRole[app.role] = (summary.byRole[app.role] || 0) + 1;
      summary.byStatus[app.status] = (summary.byStatus[app.status] || 0) + 1;
    });

    emailQueue.add({
      to: '<EMAIL>',
      subject: `Weekly Application Summary - ${summary.total} new applications`,
      template: React.createElement('div', {}, [
        React.createElement(
          'h1',
          { key: 'title' },
          'Weekly Application Summary'
        ),
        React.createElement(
          'p',
          { key: 'total' },
          `Total applications this week: ${summary.total}`
        ),
        React.createElement('h2', { key: 'by-role' }, 'By Role:'),
        ...Object.entries(summary.byRole).map(([role, count], index) =>
          React.createElement(
            'p',
            { key: `role-${index}` },
            `${role}: ${count}`
          )
        ),
        React.createElement('h2', { key: 'by-status' }, 'By Status:'),
        ...Object.entries(summary.byStatus).map(([status, count], index) =>
          React.createElement(
            'p',
            { key: `status-${index}` },
            `${status}: ${count}`
          )
        ),
      ]),
      templateName: 'weekly-application-summary',
      metadata: {
        weekStart: weekAgo.toISOString(),
        weekEnd: new Date().toISOString(),
        summary,
      },
    });
  },

  /**
   * Send NDA reminders
   */
  sendNDAReminders: async (applications: TeamApplication[]) => {
    const approvedWithoutNDA = applications.filter(
      (app) => app.status === 'accepted' && !app.is_nda_signed
    );

    for (const application of approvedWithoutNDA) {
      emailQueue.add({
        to: application.email,
        subject: 'NDA Signature Required - The Hue Factory',
        template: React.createElement('div', {}, [
          React.createElement('h1', { key: 'title' }, 'NDA Signature Required'),
          React.createElement(
            'p',
            { key: 'greeting' },
            `Hi ${application.full_name},`
          ),
          React.createElement(
            'p',
            { key: 'content' },
            'Welcome to The Hue Factory! To complete your onboarding, please sign the NDA.'
          ),
        ]),
        templateName: emailTemplates.NDA_REMINDER,
        recipientName: application.full_name,
        metadata: {
          applicationId: application.id,
          applicationIdentifier: application.identifier,
        },
      });
    }
  },
};

/**
 * Email workflow utilities
 */
export const workflowUtils = {
  /**
   * Check if email should be sent based on application state
   */
  shouldSendEmail: (
    application: TeamApplication,
    emailType: string
  ): boolean => {
    // Add business logic for when emails should be sent
    switch (emailType) {
      case emailTemplates.APPLICATION_RECEIVED:
        return true; // Always send received confirmation

      case emailTemplates.APPLICATION_APPROVED:
        return application.status === 'accepted';

      case emailTemplates.APPLICATION_REJECTED:
        return application.status === 'rejected';

      case emailTemplates.NDA_REMINDER:
        return application.status === 'accepted' && !application.is_nda_signed;

      default:
        return false;
    }
  },

  /**
   * Get email delay based on priority
   */
  getEmailDelay: (application: TeamApplication): number => {
    // Immediate for urgent, 5 minutes for high, 15 minutes for others
    switch (application.priority) {
      case 'urgent':
        return 0;
      case 'high':
        return 5 * 60 * 1000; // 5 minutes
      default:
        return 15 * 60 * 1000; // 15 minutes
    }
  },

  /**
   * Validate email workflow configuration
   */
  validateWorkflowConfig: () => {
    const requiredEnvVars = [
      'RESEND_API_KEY',
      'NEXT_PUBLIC_APP_URL',
      'NEXT_PUBLIC_MAIN_SITE_URL',
    ];

    const missing = requiredEnvVars.filter((envVar) => !process.env[envVar]);

    if (missing.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missing.join(', ')}`
      );
    }

    return true;
  },
};
