import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Text,
  Link,
  Img,
  Button,
  Hr,
  Preview,
} from '@react-email/components';
import React from 'react';

/**
 * Application Received Email Template
 * Sent to applicants when their application is received
 */

interface ApplicationReceivedEmailProps {
  applicantName: string;
  applicationId: string;
  role: string;
  submittedAt: string;
  dashboardUrl?: string;
}

export const ApplicationReceivedEmail: React.FC<ApplicationReceivedEmailProps> = ({
  applicantName,
  applicationId,
  role,
  submittedAt,
  dashboardUrl = 'https://teams.thehuefactory.co',
}) => {
  const previewText = `Your application for ${role} has been received - ${applicationId}`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Img
              src="https://thehuefactory.co/logo.png"
              width="40"
              height="40"
              alt="The Hue Factory"
              style={logo}
            />
            <Text style={headerText}>The Hue Factory</Text>
          </Section>

          {/* Main Content */}
          <Section style={content}>
            <Text style={title}>Application Received!</Text>
            
            <Text style={greeting}>Hi {applicantName},</Text>
            
            <Text style={paragraph}>
              Thank you for your interest in joining The Hue Factory! We've successfully 
              received your application for the <strong>{role}</strong> position.
            </Text>

            <Section style={applicationDetails}>
              <Text style={detailsTitle}>Application Details:</Text>
              <Text style={detail}>
                <strong>Application ID:</strong> {applicationId}
              </Text>
              <Text style={detail}>
                <strong>Role:</strong> {role}
              </Text>
              <Text style={detail}>
                <strong>Submitted:</strong> {new Date(submittedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </Section>

            <Text style={paragraph}>
              Our team will review your application carefully. We typically respond within 
              5-7 business days. You'll receive an email update as soon as we've made a decision.
            </Text>

            <Section style={buttonContainer}>
              <Button style={button} href={dashboardUrl}>
                View Application Status
              </Button>
            </Section>

            <Text style={paragraph}>
              In the meantime, feel free to explore our work and connect with us on social media. 
              We're excited to learn more about you!
            </Text>
          </Section>

          <Hr style={hr} />

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              Questions? Reply to this email or contact us at{' '}
              <Link href="mailto:<EMAIL>" style={link}>
                <EMAIL>
              </Link>
            </Text>
            
            <Text style={footerText}>
              <Link href="https://thehuefactory.co" style={link}>
                The Hue Factory
              </Link>{' '}
              • Building the future of creative collaboration
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
};

const header = {
  padding: '32px 24px 24px',
  borderBottom: '1px solid #e6ebf1',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
};

const logo = {
  borderRadius: '8px',
};

const headerText = {
  fontSize: '18px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0',
};

const content = {
  padding: '24px',
};

const title = {
  fontSize: '24px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0 0 24px',
  textAlign: 'center' as const,
};

const greeting = {
  fontSize: '16px',
  color: '#1a1a1a',
  margin: '0 0 16px',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#525252',
  margin: '0 0 16px',
};

const applicationDetails = {
  backgroundColor: '#f8fafc',
  border: '1px solid #e2e8f0',
  borderRadius: '8px',
  padding: '20px',
  margin: '24px 0',
};

const detailsTitle = {
  fontSize: '16px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0 0 12px',
};

const detail = {
  fontSize: '14px',
  color: '#525252',
  margin: '0 0 8px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#1a1a1a',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  border: 'none',
  cursor: 'pointer',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const footer = {
  padding: '0 24px',
};

const footerText = {
  fontSize: '14px',
  color: '#8898aa',
  lineHeight: '20px',
  margin: '0 0 8px',
  textAlign: 'center' as const,
};

const link = {
  color: '#1a1a1a',
  textDecoration: 'underline',
};

export default ApplicationReceivedEmail;
