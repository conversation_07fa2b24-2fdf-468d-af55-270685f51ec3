import { render } from '@react-email/render';
import { supabaseClient } from '@thf/supabase/auth/client';
import type { EmailLog } from '@thf/teams-db/types';
import { Resend } from 'resend';

/**
 * Email Service
 * Handles sending emails using Resend and logging to database
 */

const resend = new Resend(process.env.RESEND_API_KEY);
const supabase = supabaseClient();

export interface EmailOptions {
  to: string;
  subject: string;
  template: React.ReactElement;
  templateName: string;
  recipientName?: string;
  metadata?: Record<string, any>;
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Send email using Resend
 */
export const sendEmail = async (
  options: EmailOptions
): Promise<EmailResult> => {
  try {
    // Render the React email template to HTML
    const html = render(options.template);

    // Send email via Resend
    const { data, error } = await resend.emails.send({
      from: 'The Hue Factory <<EMAIL>>',
      to: options.to,
      subject: options.subject,
      html,
    });

    if (error) {
      console.error('Resend error:', error);

      // Log failed email to database
      await logEmail({
        recipient_email: options.to,
        recipient_name: options.recipientName,
        template_name: options.templateName,
        subject: options.subject,
        status: 'failed',
        error_message: error.message,
        metadata: options.metadata,
      });

      return {
        success: false,
        error: error.message,
      };
    }

    // Log successful email to database
    await logEmail({
      recipient_email: options.to,
      recipient_name: options.recipientName,
      template_name: options.templateName,
      subject: options.subject,
      status: 'sent',
      sent_at: new Date().toISOString(),
      metadata: {
        ...options.metadata,
        messageId: data?.id,
      },
    });

    return {
      success: true,
      messageId: data?.id,
    };
  } catch (error) {
    console.error('Email service error:', error);

    // Log failed email to database
    await logEmail({
      recipient_email: options.to,
      recipient_name: options.recipientName,
      template_name: options.templateName,
      subject: options.subject,
      status: 'failed',
      error_message: error instanceof Error ? error.message : 'Unknown error',
      metadata: options.metadata,
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Log email to database
 */
const logEmail = async (emailData: Omit<EmailLog, 'id' | 'created_at'>) => {
  try {
    const { error } = await supabase.from('Emails').insert({
      ...emailData,
      created_at: new Date().toISOString(),
    });

    if (error) {
      console.error('Failed to log email:', error);
    }
  } catch (error) {
    console.error('Failed to log email:', error);
  }
};

/**
 * Email Templates Registry
 */
export const emailTemplates = {
  APPLICATION_RECEIVED: 'application-received',
  APPLICATION_APPROVED: 'application-approved',
  APPLICATION_REJECTED: 'application-rejected',
  APPLICATION_UNDER_REVIEW: 'application-under-review',
  NDA_REMINDER: 'nda-reminder',
  TEAM_WELCOME: 'team-welcome',
  PROPOSAL_SUBMITTED: 'proposal-submitted',
  PROPOSAL_APPROVED: 'proposal-approved',
  PROPOSAL_REJECTED: 'proposal-rejected',
} as const;

/**
 * Email Queue for batch processing
 */
interface QueuedEmail extends EmailOptions {
  id: string;
  scheduledFor?: Date;
  retryCount?: number;
}

class EmailQueue {
  private queue: QueuedEmail[] = [];
  private processing = false;

  /**
   * Add email to queue
   */
  add(email: Omit<QueuedEmail, 'id'>) {
    const queuedEmail: QueuedEmail = {
      ...email,
      id: crypto.randomUUID(),
      retryCount: 0,
    };

    this.queue.push(queuedEmail);
    this.processQueue();
  }

  /**
   * Process email queue
   */
  private async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const email = this.queue.shift();
      if (!email) continue;

      // Check if email is scheduled for later
      if (email.scheduledFor && email.scheduledFor > new Date()) {
        this.queue.push(email); // Put it back in queue
        continue;
      }

      try {
        const result = await sendEmail(email);

        if (!result.success && (email.retryCount || 0) < 3) {
          // Retry failed emails up to 3 times
          email.retryCount = (email.retryCount || 0) + 1;
          email.scheduledFor = new Date(Date.now() + 5 * 60 * 1000); // Retry in 5 minutes
          this.queue.push(email);
        }
      } catch (error) {
        console.error('Failed to process email:', error);
      }

      // Add delay between emails to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    this.processing = false;
  }

  /**
   * Get queue status
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      processing: this.processing,
    };
  }
}

export const emailQueue = new EmailQueue();

/**
 * Email validation utilities
 */
export const emailUtils = {
  /**
   * Validate email address
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Sanitize email address
   */
  sanitizeEmail: (email: string): string => {
    return email.toLowerCase().trim();
  },

  /**
   * Generate email subject with application ID
   */
  generateSubject: (template: string, applicationId?: string): string => {
    const subjects = {
      [emailTemplates.APPLICATION_RECEIVED]: `Application Received - ${applicationId}`,
      [emailTemplates.APPLICATION_APPROVED]: `Welcome to The Hue Factory! - ${applicationId}`,
      [emailTemplates.APPLICATION_REJECTED]: `Application Update - ${applicationId}`,
      [emailTemplates.APPLICATION_UNDER_REVIEW]: `Application Under Review - ${applicationId}`,
      [emailTemplates.NDA_REMINDER]: 'NDA Signature Required',
      [emailTemplates.TEAM_WELCOME]: 'Welcome to The Hue Factory Team!',
      [emailTemplates.PROPOSAL_SUBMITTED]: 'Proposal Submitted Successfully',
      [emailTemplates.PROPOSAL_APPROVED]: 'Proposal Approved!',
      [emailTemplates.PROPOSAL_REJECTED]: 'Proposal Update',
    };

    return (
      subjects[template as keyof typeof subjects] || 'The Hue Factory Update'
    );
  },

  /**
   * Get email template name from type
   */
  getTemplateName: (template: string): string => {
    return (
      Object.entries(emailTemplates).find(
        ([, value]) => value === template
      )?.[0] || template
    );
  },
};

/**
 * Email analytics
 */
export const emailAnalytics = {
  /**
   * Get email statistics
   */
  getStats: async (dateRange?: { from: string; to: string }) => {
    let query = supabase
      .from('Emails')
      .select('status, template_name, created_at');

    if (dateRange) {
      query = query
        .gte('created_at', dateRange.from)
        .lte('created_at', dateRange.to);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get email stats: ${error.message}`);
    }

    const stats = {
      total: data.length,
      sent: data.filter((email) => email.status === 'sent').length,
      failed: data.filter((email) => email.status === 'failed').length,
      pending: data.filter((email) => email.status === 'pending').length,
      byTemplate: {} as Record<string, number>,
    };

    data.forEach((email) => {
      stats.byTemplate[email.template_name] =
        (stats.byTemplate[email.template_name] || 0) + 1;
    });

    return stats;
  },

  /**
   * Get delivery rate
   */
  getDeliveryRate: async (templateName?: string) => {
    let query = supabase.from('Emails').select('status');

    if (templateName) {
      query = query.eq('template_name', templateName);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get delivery rate: ${error.message}`);
    }

    const total = data.length;
    const delivered = data.filter(
      (email) => email.status === 'sent' || email.status === 'delivered'
    ).length;

    return total > 0 ? (delivered / total) * 100 : 0;
  },
};
