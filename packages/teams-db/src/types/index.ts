/**
 * Database Types for Teams Application
 * TypeScript definitions for all database entities
 */

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: TeamProfile;
        Insert: Omit<TeamProfile, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<TeamProfile, 'id' | 'created_at'>>;
      };
      JOIN_US_TABLE: {
        Row: TeamApplication;
        Insert: Omit<TeamApplication, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<TeamApplication, 'id' | 'created_at'>>;
      };
      affiliate_proposals: {
        Row: AffiliateProposal;
        Insert: Omit<AffiliateProposal, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<AffiliateProposal, 'id' | 'created_at'>>;
      };
      waitlists: {
        Row: WaitlistEntry;
        Insert: Omit<WaitlistEntry, 'id' | 'created_at'>;
        Update: Partial<Omit<WaitlistEntry, 'id' | 'created_at'>>;
      };
      Emails: {
        Row: EmailLog;
        Insert: Omit<EmailLog, 'id' | 'created_at'>;
        Update: Partial<Omit<EmailLog, 'id' | 'created_at'>>;
      };
    };
    Enums: {
      role_type: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
      application_status: 'received' | 'reviewed' | 'accepted' | 'rejected';
      proposal_status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
      email_status: 'pending' | 'sent' | 'delivered' | 'failed';
    };
  };
}

/**
 * Team Profile
 */
export interface TeamProfile {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  role: Database['public']['Enums']['role_type'];
  avatar_url?: string;
  is_nda_signed: boolean;
  nda_signed_date?: string;
  created_at: string;
  updated_at: string;
  skills?: string[];
  bio?: string;
  portfolio_url?: string;
  phone?: string;
  location?: string;
  timezone?: string;
  availability?: 'full-time' | 'part-time' | 'project-based';
}

/**
 * Team Application
 */
export interface TeamApplication {
  id: string;
  identifier: string;
  full_name: string;
  email: string;
  role: Database['public']['Enums']['role_type'];
  status: Database['public']['Enums']['application_status'];
  priority?: 'urgent' | 'high' | 'medium' | 'low' | 'no-priority';
  experience: string;
  skills: string[];
  portfolio_url?: string;
  motivation: string;
  availability: 'full-time' | 'part-time' | 'project-based';
  is_nda_signed?: boolean;
  nda_signed_date?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  phone?: string;
  location?: string;
  linkedin_url?: string;
  github_url?: string;
  years_of_experience?: number;
  expected_start_date?: string;
}

/**
 * Affiliate Proposal
 */
export interface AffiliateProposal {
  id: string;
  user_id: string;
  title: string;
  description: string;
  status: Database['public']['Enums']['proposal_status'];
  category: string;
  budget_range?: string;
  timeline?: string;
  deliverables: string[];
  requirements: string[];
  attachments?: string[];
  submitted_by: string;
  reviewed_by?: string;
  reviewed_at?: string;
  feedback?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Waitlist Entry
 */
export interface WaitlistEntry {
  id: string;
  email: string;
  full_name?: string;
  role_interest?: Database['public']['Enums']['role_type'];
  source?: string;
  utm_campaign?: string;
  utm_source?: string;
  utm_medium?: string;
  is_launch_day_sent: boolean;
  launch_day_sent_at?: string;
  created_at: string;
}

/**
 * Email Log
 */
export interface EmailLog {
  id: string;
  recipient_email: string;
  recipient_name?: string;
  template_name: string;
  subject: string;
  status: Database['public']['Enums']['email_status'];
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

/**
 * Application Status with UI Properties
 */
export interface ApplicationStatusWithUI {
  id: Database['public']['Enums']['application_status'];
  name: string;
  color: string;
  description: string;
}

export const applicationStatuses: ApplicationStatusWithUI[] = [
  {
    id: 'received',
    name: 'Received',
    color: '#6b7280',
    description: 'Application has been received and is pending review',
  },
  {
    id: 'reviewed',
    name: 'Under Review',
    color: '#3b82f6',
    description: 'Application is currently being reviewed by the team',
  },
  {
    id: 'accepted',
    name: 'Accepted',
    color: '#10b981',
    description: 'Application has been accepted',
  },
  {
    id: 'rejected',
    name: 'Rejected',
    color: '#ef4444',
    description: 'Application has been rejected',
  },
];

/**
 * Priority with UI Properties
 */
export interface PriorityWithUI {
  id: 'urgent' | 'high' | 'medium' | 'low' | 'no-priority';
  name: string;
  color: string;
  level: number;
}

export const priorities: PriorityWithUI[] = [
  { id: 'no-priority', name: 'No priority', color: '#9ca3af', level: 0 },
  { id: 'low', name: 'Low', color: '#6b7280', level: 1 },
  { id: 'medium', name: 'Medium', color: '#facc15', level: 2 },
  { id: 'high', name: 'High', color: '#f97316', level: 3 },
  { id: 'urgent', name: 'Urgent', color: '#ef4444', level: 4 },
];

/**
 * Query Filters
 */
export interface ApplicationFilters {
  status?: Database['public']['Enums']['application_status'][];
  role?: Database['public']['Enums']['role_type'][];
  priority?: ('urgent' | 'high' | 'medium' | 'low' | 'no-priority')[];
  dateRange?: {
    from: string;
    to: string;
  };
  search?: string;
  isNdaSigned?: boolean;
}

export interface ProposalFilters {
  status?: Database['public']['Enums']['proposal_status'][];
  category?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
  search?: string;
}

/**
 * Sort Options
 */
export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Pagination
 */
export interface PaginationOptions {
  page: number;
  limit: number;
}

/**
 * Query Response
 */
export interface QueryResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Utility Types
 */
export type CreateApplicationData = Omit<TeamApplication, 'id' | 'identifier' | 'created_at' | 'updated_at' | 'status'>;
export type UpdateApplicationData = Partial<Pick<TeamApplication, 'status' | 'priority' | 'notes' | 'reviewed_by' | 'reviewed_at'>>;
export type CreateProposalData = Omit<AffiliateProposal, 'id' | 'created_at' | 'updated_at' | 'status'>;
export type UpdateProposalData = Partial<Pick<AffiliateProposal, 'status' | 'feedback' | 'reviewed_by' | 'reviewed_at'>>;

/**
 * Real-time Event Types
 */
export interface RealtimeEvent<T = any> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  table: string;
}

export type ApplicationRealtimeEvent = RealtimeEvent<TeamApplication>;
export type ProposalRealtimeEvent = RealtimeEvent<AffiliateProposal>;
export type ProfileRealtimeEvent = RealtimeEvent<TeamProfile>;
