import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { createClient } from '@thf/supabase/client';
import type {
  TeamApplication,
  ApplicationFilters,
  SortOption,
  PaginationOptions,
  QueryResponse,
} from '../types';

/**
 * Application Queries
 * React Query hooks for fetching application data
 */

const supabase = createClient();

/**
 * Query Keys
 */
export const applicationKeys = {
  all: ['applications'] as const,
  lists: () => [...applicationKeys.all, 'list'] as const,
  list: (filters: ApplicationFilters) => [...applicationKeys.lists(), filters] as const,
  details: () => [...applicationKeys.all, 'detail'] as const,
  detail: (id: string) => [...applicationKeys.details(), id] as const,
  stats: () => [...applicationKeys.all, 'stats'] as const,
};

/**
 * Fetch applications with filters, sorting, and pagination
 */
export const fetchApplications = async (
  filters: ApplicationFilters = {},
  sort: SortOption = { field: 'created_at', direction: 'desc' },
  pagination: PaginationOptions = { page: 1, limit: 50 }
): Promise<QueryResponse<TeamApplication>> => {
  let query = supabase
    .from('JOIN_US_TABLE')
    .select('*', { count: 'exact' });

  // Apply filters
  if (filters.status && filters.status.length > 0) {
    query = query.in('status', filters.status);
  }

  if (filters.role && filters.role.length > 0) {
    query = query.in('role', filters.role);
  }

  if (filters.priority && filters.priority.length > 0) {
    query = query.in('priority', filters.priority);
  }

  if (filters.isNdaSigned !== undefined) {
    query = query.eq('is_nda_signed', filters.isNdaSigned);
  }

  if (filters.dateRange) {
    query = query
      .gte('created_at', filters.dateRange.from)
      .lte('created_at', filters.dateRange.to);
  }

  if (filters.search) {
    query = query.or(
      `full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,identifier.ilike.%${filters.search}%`
    );
  }

  // Apply sorting
  query = query.order(sort.field, { ascending: sort.direction === 'asc' });

  // Apply pagination
  const from = (pagination.page - 1) * pagination.limit;
  const to = from + pagination.limit - 1;
  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch applications: ${error.message}`);
  }

  const totalPages = Math.ceil((count || 0) / pagination.limit);

  return {
    data: data || [],
    count: count || 0,
    page: pagination.page,
    limit: pagination.limit,
    totalPages,
  };
};

/**
 * Hook to fetch applications
 */
export const useApplications = (
  filters: ApplicationFilters = {},
  sort: SortOption = { field: 'created_at', direction: 'desc' },
  pagination: PaginationOptions = { page: 1, limit: 50 }
) => {
  return useQuery({
    queryKey: applicationKeys.list({ ...filters, sort, pagination } as any),
    queryFn: () => fetchApplications(filters, sort, pagination),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to fetch applications with infinite scroll
 */
export const useInfiniteApplications = (
  filters: ApplicationFilters = {},
  sort: SortOption = { field: 'created_at', direction: 'desc' },
  limit: number = 20
) => {
  return useInfiniteQuery({
    queryKey: [...applicationKeys.list(filters), 'infinite', sort, limit],
    queryFn: ({ pageParam = 1 }) =>
      fetchApplications(filters, sort, { page: pageParam, limit }),
    getNextPageParam: (lastPage) => {
      if (lastPage.page < lastPage.totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

/**
 * Fetch single application by ID
 */
export const fetchApplication = async (id: string): Promise<TeamApplication> => {
  const { data, error } = await supabase
    .from('JOIN_US_TABLE')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(`Failed to fetch application: ${error.message}`);
  }

  return data;
};

/**
 * Hook to fetch single application
 */
export const useApplication = (id: string) => {
  return useQuery({
    queryKey: applicationKeys.detail(id),
    queryFn: () => fetchApplication(id),
    enabled: !!id,
  });
};

/**
 * Fetch application statistics
 */
export const fetchApplicationStats = async () => {
  const { data, error } = await supabase
    .from('JOIN_US_TABLE')
    .select('status, role, created_at, is_nda_signed');

  if (error) {
    throw new Error(`Failed to fetch application stats: ${error.message}`);
  }

  const stats = {
    total: data.length,
    byStatus: {} as Record<string, number>,
    byRole: {} as Record<string, number>,
    ndaSigned: 0,
    thisMonth: 0,
    thisWeek: 0,
  };

  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));

  data.forEach((app) => {
    // Count by status
    stats.byStatus[app.status] = (stats.byStatus[app.status] || 0) + 1;

    // Count by role
    stats.byRole[app.role] = (stats.byRole[app.role] || 0) + 1;

    // Count NDA signed
    if (app.is_nda_signed) {
      stats.ndaSigned++;
    }

    // Count this month
    const createdAt = new Date(app.created_at);
    if (createdAt >= startOfMonth) {
      stats.thisMonth++;
    }

    // Count this week
    if (createdAt >= startOfWeek) {
      stats.thisWeek++;
    }
  });

  return stats;
};

/**
 * Hook to fetch application statistics
 */
export const useApplicationStats = () => {
  return useQuery({
    queryKey: applicationKeys.stats(),
    queryFn: fetchApplicationStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Search applications
 */
export const searchApplications = async (
  query: string,
  limit: number = 10
): Promise<TeamApplication[]> => {
  const { data, error } = await supabase
    .from('JOIN_US_TABLE')
    .select('*')
    .or(
      `full_name.ilike.%${query}%,email.ilike.%${query}%,identifier.ilike.%${query}%,skills.cs.{${query}}`
    )
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    throw new Error(`Failed to search applications: ${error.message}`);
  }

  return data || [];
};

/**
 * Hook to search applications
 */
export const useSearchApplications = (query: string, limit: number = 10) => {
  return useQuery({
    queryKey: [...applicationKeys.all, 'search', query, limit],
    queryFn: () => searchApplications(query, limit),
    enabled: query.length > 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Fetch recent applications
 */
export const fetchRecentApplications = async (limit: number = 5): Promise<TeamApplication[]> => {
  const { data, error } = await supabase
    .from('JOIN_US_TABLE')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    throw new Error(`Failed to fetch recent applications: ${error.message}`);
  }

  return data || [];
};

/**
 * Hook to fetch recent applications
 */
export const useRecentApplications = (limit: number = 5) => {
  return useQuery({
    queryKey: [...applicationKeys.all, 'recent', limit],
    queryFn: () => fetchRecentApplications(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
