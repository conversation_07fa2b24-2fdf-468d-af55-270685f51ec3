import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabaseClient } from '@thf/supabase/auth/client';
import { applicationKeys } from '../queries/applications';
import type {
  CreateApplicationData,
  Database,
  TeamApplication,
  UpdateApplicationData,
} from '../types';

/**
 * Application Mutations
 * React Query mutations for modifying application data
 */

const supabase = supabaseClient();

/**
 * Generate unique application identifier
 */
const generateApplicationIdentifier = async (): Promise<string> => {
  const { count } = await supabase
    .from('JOIN_US_TABLE')
    .select('*', { count: 'exact', head: true });

  const nextNumber = (count || 0) + 1;
  return `APP-${nextNumber.toString().padStart(4, '0')}`;
};

/**
 * Create new application
 */
export const createApplication = async (
  data: CreateApplicationData
): Promise<TeamApplication> => {
  const identifier = await generateApplicationIdentifier();

  const applicationData = {
    ...data,
    identifier,
    status: 'received' as Database['public']['Enums']['application_status'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const { data: newApplication, error } = await supabase
    .from('JOIN_US_TABLE')
    .insert(applicationData)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create application: ${error.message}`);
  }

  return newApplication;
};

/**
 * Hook to create application
 */
export const useCreateApplication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createApplication,
    onSuccess: (newApplication) => {
      // Invalidate and refetch applications list
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });

      // Add to cache
      queryClient.setQueryData(
        applicationKeys.detail(newApplication.id),
        newApplication
      );

      // Update stats
      queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
    },
  });
};

/**
 * Update application
 */
export const updateApplication = async (
  id: string,
  updates: UpdateApplicationData
): Promise<TeamApplication> => {
  const updateData = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const { data, error } = await supabase
    .from('JOIN_US_TABLE')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update application: ${error.message}`);
  }

  return data;
};

/**
 * Hook to update application
 */
export const useUpdateApplication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      updates,
    }: {
      id: string;
      updates: UpdateApplicationData;
    }) => updateApplication(id, updates),
    onSuccess: (updatedApplication) => {
      // Update specific application in cache
      queryClient.setQueryData(
        applicationKeys.detail(updatedApplication.id),
        updatedApplication
      );

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });

      // Update stats if status changed
      queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
    },
  });
};

/**
 * Update application status
 */
export const updateApplicationStatus = async (
  id: string,
  status: Database['public']['Enums']['application_status'],
  reviewedBy?: string
): Promise<TeamApplication> => {
  const updates: UpdateApplicationData = {
    status,
    updated_at: new Date().toISOString(),
  };

  if (reviewedBy) {
    updates.reviewed_by = reviewedBy;
    updates.reviewed_at = new Date().toISOString();
  }

  return updateApplication(id, updates);
};

/**
 * Hook to update application status
 */
export const useUpdateApplicationStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      status,
      reviewedBy,
    }: {
      id: string;
      status: Database['public']['Enums']['application_status'];
      reviewedBy?: string;
    }) => updateApplicationStatus(id, status, reviewedBy),
    onSuccess: (updatedApplication) => {
      queryClient.setQueryData(
        applicationKeys.detail(updatedApplication.id),
        updatedApplication
      );
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
    },
  });
};

/**
 * Approve application
 */
export const approveApplication = async (
  id: string,
  reviewedBy: string
): Promise<TeamApplication> => {
  return updateApplicationStatus(id, 'accepted', reviewedBy);
};

/**
 * Hook to approve application
 */
export const useApproveApplication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reviewedBy }: { id: string; reviewedBy: string }) =>
      approveApplication(id, reviewedBy),
    onSuccess: (updatedApplication) => {
      queryClient.setQueryData(
        applicationKeys.detail(updatedApplication.id),
        updatedApplication
      );
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
    },
  });
};

/**
 * Reject application
 */
export const rejectApplication = async (
  id: string,
  reviewedBy: string,
  notes?: string
): Promise<TeamApplication> => {
  const updates: UpdateApplicationData = {
    status: 'rejected',
    reviewed_by: reviewedBy,
    reviewed_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  if (notes) {
    updates.notes = notes;
  }

  return updateApplication(id, updates);
};

/**
 * Hook to reject application
 */
export const useRejectApplication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      reviewedBy,
      notes,
    }: {
      id: string;
      reviewedBy: string;
      notes?: string;
    }) => rejectApplication(id, reviewedBy, notes),
    onSuccess: (updatedApplication) => {
      queryClient.setQueryData(
        applicationKeys.detail(updatedApplication.id),
        updatedApplication
      );
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
    },
  });
};

/**
 * Update application priority
 */
export const updateApplicationPriority = async (
  id: string,
  priority: 'urgent' | 'high' | 'medium' | 'low' | 'no-priority'
): Promise<TeamApplication> => {
  return updateApplication(id, { priority });
};

/**
 * Hook to update application priority
 */
export const useUpdateApplicationPriority = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      priority,
    }: {
      id: string;
      priority: 'urgent' | 'high' | 'medium' | 'low' | 'no-priority';
    }) => updateApplicationPriority(id, priority),
    onSuccess: (updatedApplication) => {
      queryClient.setQueryData(
        applicationKeys.detail(updatedApplication.id),
        updatedApplication
      );
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });
    },
  });
};

/**
 * Add notes to application
 */
export const addApplicationNotes = async (
  id: string,
  notes: string
): Promise<TeamApplication> => {
  return updateApplication(id, { notes });
};

/**
 * Hook to add notes to application
 */
export const useAddApplicationNotes = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, notes }: { id: string; notes: string }) =>
      addApplicationNotes(id, notes),
    onSuccess: (updatedApplication) => {
      queryClient.setQueryData(
        applicationKeys.detail(updatedApplication.id),
        updatedApplication
      );
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });
    },
  });
};

/**
 * Delete application
 */
export const deleteApplication = async (id: string): Promise<void> => {
  const { error } = await supabase.from('JOIN_US_TABLE').delete().eq('id', id);

  if (error) {
    throw new Error(`Failed to delete application: ${error.message}`);
  }
};

/**
 * Hook to delete application
 */
export const useDeleteApplication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteApplication,
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: applicationKeys.detail(deletedId),
      });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });

      // Update stats
      queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
    },
  });
};

/**
 * Bulk update applications
 */
export const bulkUpdateApplications = async (
  ids: string[],
  updates: UpdateApplicationData
): Promise<TeamApplication[]> => {
  const updateData = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const { data, error } = await supabase
    .from('JOIN_US_TABLE')
    .update(updateData)
    .in('id', ids)
    .select();

  if (error) {
    throw new Error(`Failed to bulk update applications: ${error.message}`);
  }

  return data || [];
};

/**
 * Hook to bulk update applications
 */
export const useBulkUpdateApplications = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      ids,
      updates,
    }: {
      ids: string[];
      updates: UpdateApplicationData;
    }) => bulkUpdateApplications(ids, updates),
    onSuccess: () => {
      // Invalidate all application queries
      queryClient.invalidateQueries({ queryKey: applicationKeys.all });
    },
  });
};
