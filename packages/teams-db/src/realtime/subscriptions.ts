import { useQueryClient } from '@tanstack/react-query';
import { supabaseClient } from '@thf/supabase/auth/client';
import { useEffect, useRef } from 'react';
import { applicationKeys } from '../queries/applications';
import type {
  ApplicationRealtimeEvent,
  ProfileRealtimeEvent,
  ProposalRealtimeEvent,
  TeamApplication,
} from '../types';

/**
 * Real-time Subscriptions
 * Hooks for subscribing to real-time database changes
 */

const supabase = supabaseClient();

/**
 * Hook to subscribe to application changes
 */
export const useApplicationsRealtime = () => {
  const queryClient = useQueryClient();
  const channelRef = useRef<any>(null);

  useEffect(() => {
    // Create channel for applications
    channelRef.current = supabase
      .channel('applications-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'JOIN_US_TABLE',
        },
        (payload: ApplicationRealtimeEvent) => {
          console.log('Application change received:', payload);

          switch (payload.eventType) {
            case 'INSERT':
              handleApplicationInsert(payload.new, queryClient);
              break;
            case 'UPDATE':
              handleApplicationUpdate(payload.new, queryClient);
              break;
            case 'DELETE':
              handleApplicationDelete(payload.old, queryClient);
              break;
          }
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [queryClient]);
};

/**
 * Handle application insert
 */
const handleApplicationInsert = (
  newApplication: TeamApplication,
  queryClient: any
) => {
  // Add to detail cache
  queryClient.setQueryData(
    applicationKeys.detail(newApplication.id),
    newApplication
  );

  // Invalidate lists to include new application
  queryClient.invalidateQueries({ queryKey: applicationKeys.lists() });

  // Update stats
  queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });

  // Update recent applications
  queryClient.invalidateQueries({
    queryKey: [...applicationKeys.all, 'recent'],
  });
};

/**
 * Handle application update
 */
const handleApplicationUpdate = (
  updatedApplication: TeamApplication,
  queryClient: any
) => {
  // Update detail cache
  queryClient.setQueryData(
    applicationKeys.detail(updatedApplication.id),
    updatedApplication
  );

  // Update in lists cache
  queryClient.setQueriesData(
    { queryKey: applicationKeys.lists() },
    (oldData: any) => {
      if (!oldData?.data) return oldData;

      return {
        ...oldData,
        data: oldData.data.map((app: TeamApplication) =>
          app.id === updatedApplication.id ? updatedApplication : app
        ),
      };
    }
  );

  // Update stats if status changed
  queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
};

/**
 * Handle application delete
 */
const handleApplicationDelete = (
  deletedApplication: TeamApplication,
  queryClient: any
) => {
  // Remove from detail cache
  queryClient.removeQueries({
    queryKey: applicationKeys.detail(deletedApplication.id),
  });

  // Remove from lists cache
  queryClient.setQueriesData(
    { queryKey: applicationKeys.lists() },
    (oldData: any) => {
      if (!oldData?.data) return oldData;

      return {
        ...oldData,
        data: oldData.data.filter(
          (app: TeamApplication) => app.id !== deletedApplication.id
        ),
        count: oldData.count - 1,
      };
    }
  );

  // Update stats
  queryClient.invalidateQueries({ queryKey: applicationKeys.stats() });
};

/**
 * Hook to subscribe to proposal changes
 */
export const useProposalsRealtime = () => {
  const queryClient = useQueryClient();
  const channelRef = useRef<any>(null);

  useEffect(() => {
    channelRef.current = supabase
      .channel('proposals-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'affiliate_proposals',
        },
        (payload: ProposalRealtimeEvent) => {
          console.log('Proposal change received:', payload);

          switch (payload.eventType) {
            case 'INSERT':
              queryClient.invalidateQueries({ queryKey: ['proposals'] });
              break;
            case 'UPDATE':
              queryClient.setQueryData(
                ['proposals', 'detail', payload.new.id],
                payload.new
              );
              queryClient.invalidateQueries({
                queryKey: ['proposals', 'list'],
              });
              break;
            case 'DELETE':
              queryClient.removeQueries({
                queryKey: ['proposals', 'detail', payload.old.id],
              });
              queryClient.invalidateQueries({
                queryKey: ['proposals', 'list'],
              });
              break;
          }
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [queryClient]);
};

/**
 * Hook to subscribe to profile changes
 */
export const useProfilesRealtime = () => {
  const queryClient = useQueryClient();
  const channelRef = useRef<any>(null);

  useEffect(() => {
    channelRef.current = supabase
      .channel('profiles-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles',
        },
        (payload: ProfileRealtimeEvent) => {
          console.log('Profile change received:', payload);

          switch (payload.eventType) {
            case 'INSERT':
              queryClient.invalidateQueries({ queryKey: ['profiles'] });
              break;
            case 'UPDATE':
              queryClient.setQueryData(
                ['profiles', 'detail', payload.new.id],
                payload.new
              );
              queryClient.invalidateQueries({ queryKey: ['profiles', 'list'] });
              break;
            case 'DELETE':
              queryClient.removeQueries({
                queryKey: ['profiles', 'detail', payload.old.id],
              });
              queryClient.invalidateQueries({ queryKey: ['profiles', 'list'] });
              break;
          }
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [queryClient]);
};

/**
 * Hook to subscribe to all team-related changes
 */
export const useTeamsRealtime = () => {
  useApplicationsRealtime();
  useProposalsRealtime();
  useProfilesRealtime();
};

/**
 * Real-time notification hook
 */
export const useRealtimeNotifications = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const channel = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'JOIN_US_TABLE',
        },
        (payload) => {
          // Show notification for new applications
          if (typeof window !== 'undefined' && 'Notification' in window) {
            if (Notification.permission === 'granted') {
              new Notification('New Application Received', {
                body: `${payload.new.full_name} applied for ${payload.new.role}`,
                icon: '/favicon.ico',
              });
            }
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'JOIN_US_TABLE',
        },
        (payload) => {
          // Show notification for status changes
          if (payload.new.status !== payload.old.status) {
            if (typeof window !== 'undefined' && 'Notification' in window) {
              if (Notification.permission === 'granted') {
                new Notification('Application Status Updated', {
                  body: `${payload.new.full_name}'s application is now ${payload.new.status}`,
                  icon: '/favicon.ico',
                });
              }
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);
};

/**
 * Request notification permission
 */
export const requestNotificationPermission = async (): Promise<boolean> => {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission === 'denied') {
    return false;
  }

  const permission = await Notification.requestPermission();
  return permission === 'granted';
};

/**
 * Real-time connection status
 */
export const useRealtimeStatus = () => {
  const channelRef = useRef<any>(null);

  useEffect(() => {
    channelRef.current = supabase
      .channel('connection-status')
      .on('presence', { event: 'sync' }, () => {
        console.log('Real-time connection synced');
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', key, leftPresences);
      })
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, []);

  return {
    isConnected: channelRef.current?.state === 'joined',
  };
};
