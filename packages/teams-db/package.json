{"name": "@thf/teams-db", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./types": "./src/types/index.ts", "./queries/*": "./src/queries/*.ts", "./mutations/*": "./src/mutations/*.ts"}, "dependencies": {"react": "^19.1.0", "@thf/supabase": "*", "@tanstack/react-query": "^5.0.0", "zod": "^3.24.2"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^19.1.0", "eslint": "^9.28.0", "typescript": "5.8.2"}}