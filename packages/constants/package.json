{"name": "@thf/constants", "version": "0.0.0", "type": "module", "private": true, "exports": {"./fonts": "./fonts.ts", "./variables": "./variables.ts", "./framer-motion": "./framer-motion.ts", "./gh-phone": "./gh-phone.ts", "./nav-sizes": "./nav-sizes.ts"}, "dependencies": {"clsx": "^2.1.1", "tailwind-merge": "^3.0.2"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "eslint": "^9.28.0", "typescript": "5.8.2"}}