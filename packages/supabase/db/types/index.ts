export * from './types';

type WorkTypes =
  | 'Graphic Design'
  | 'Website Development'
  | 'App Development'
  | 'Brand Development';

type VolunteerAreaTypes =
  | 'Design (Graphic/Web)'
  | 'Content Creation (Social Media, Blog)'
  | 'Event Support'
  | 'Community Engagement & Outreach'
  | 'Branding Strategy'
  | 'Photography/Videography'
  | 'Other (Please Specify)';

type VolunteerAvailabilityTypes =
  | 'Less than 5 hours'
  | '5-10 hours'
  | '10-15 hours'
  | '15+ hours';

type VolunteerDaysAvailabilityTypes =
  | 'Monday'
  | 'Tuesday'
  | 'Wednesday'
  | 'Thursday'
  | 'Friday'
  | 'Saturday'
  | 'Sunday';

type VolunteerTimeOfTheDayAvailabilityTypes =
  | 'Morning'
  | 'Afternoon'
  | 'Evening';

export type ReferalProposalType = {
  proposal_type: string | WorkTypes;
  client_name: string;
  proposal_message: string;
};

export type VolunteerDetailsType = {
  dob: string;
  about_you: string;
  interest_in_volunteering: string;
  relivant_skills: string;
  contribution_area: string | VolunteerAreaTypes;
  reason_for_joining: string;
  past_volunteering: string;
  hours_per_week: VolunteerAvailabilityTypes;
  days_per_week: VolunteerDaysAvailabilityTypes[];
  prefered_time_of_day: VolunteerTimeOfTheDayAvailabilityTypes;
  access_to_equipments: boolean;
  other_details: string;
  subscribe_to_newsletter: boolean;
};
