{"name": "@thf/supabase", "version": "0.0.0", "type": "module", "private": true, "exports": {"./auth/client": "./auth/client.ts", "./auth/server": "./auth/server.ts", "./auth/middleware": "./auth/middleware.ts", "./auth/getUser": "./auth/getUser.ts", "./db/types": "./db/types/index.ts"}, "devDependencies": {"@supabase/ssr": "^0.6.1", "@thf/eslint-config": "*", "@thf/typescript-config": "*", "eslint": "^9.28.0", "typescript": "5.8.2"}}