{"name": "@thf/supabase", "version": "0.0.0", "type": "module", "private": true, "exports": {"./auth/client": "./auth/client.ts", "./auth/server": "./auth/server.ts", "./auth/middleware": "./auth/middleware.ts", "./auth/getUser": "./auth/getUser.ts", "./db/types": "./db/types/index.ts"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "eslint": "^9.29.0", "typescript": "5.8.2"}}