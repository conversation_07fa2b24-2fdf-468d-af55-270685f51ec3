{"name": "@thf/teams-ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "tsc", "check-types": "tsc --noEmit", "dev": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "exports": {"./components/*": "./src/components/*.tsx", "./layouts/*": "./src/layouts/*.tsx", "./forms/*": "./src/forms/*.tsx", "./providers/*": "./src/providers/*.tsx"}, "dependencies": {"react": "^19.1.0", "@thf/ui": "*", "@thf/theme": "*", "@thf/icons": "*", "@thf/animations": "*", "@thf/teams-db": "*", "@thf/teams-auth": "*", "@radix-ui/react-context-menu": "^2.2.10", "@radix-ui/react-dialog": "^1.1.3", "@tanstack/react-query": "^5.0.0", "framer-motion": "^11.11.4", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "date-fns": "^4.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1"}, "devDependencies": {"@thf/eslint-config": "*", "@thf/typescript-config": "*", "@types/react": "^19.1.0", "eslint": "^9.28.0", "typescript": "5.8.2"}}