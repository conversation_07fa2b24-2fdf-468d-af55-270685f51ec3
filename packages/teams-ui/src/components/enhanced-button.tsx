'use client';

import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { motion, type MotionProps } from 'framer-motion';
import { cn } from '@thf/ui/utils/cn';

/**
 * Enhanced <PERSON>ton Component
 * Based on Circle-temp's sophisticated button system with comprehensive variants
 */

const enhancedButtonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90',
        outline: 'border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        // Circle-temp inspired variants
        sidebar: 'bg-sidebar-accent text-sidebar-accent-foreground hover:bg-sidebar-accent/80',
        success: 'bg-green-600 text-white shadow-xs hover:bg-green-700',
        warning: 'bg-yellow-600 text-white shadow-xs hover:bg-yellow-700',
        info: 'bg-blue-600 text-white shadow-xs hover:bg-blue-700',
      },
      size: {
        default: 'h-9 px-4 py-2',
        xxs: 'h-6 rounded-md gap-1.5 px-2.5 text-xs',
        xs: 'h-7 rounded-md gap-1.5 px-2.5 text-xs',
        sm: 'h-8 rounded-md gap-1.5 px-3 text-xs',
        lg: 'h-10 rounded-md px-6 text-base',
        icon: 'size-9',
        'icon-xs': 'size-6',
        'icon-sm': 'size-7',
        'icon-lg': 'size-10',
      },
      animation: {
        none: '',
        hover: 'hover:scale-[1.02] active:scale-[0.98]',
        press: 'active:scale-[0.95]',
        lift: 'hover:-translate-y-0.5 hover:shadow-md',
        bounce: 'hover:animate-bounce',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      animation: 'hover',
    },
  }
);

export interface EnhancedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof enhancedButtonVariants>,
    Omit<MotionProps, 'children'> {
  asChild?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  motionProps?: MotionProps;
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    animation,
    asChild = false, 
    loading = false,
    leftIcon,
    rightIcon,
    children,
    disabled,
    motionProps,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : motion.button;
    const isDisabled = disabled || loading;

    const defaultMotionProps: MotionProps = {
      whileHover: animation === 'hover' ? { scale: 1.02 } : undefined,
      whileTap: animation === 'press' || animation === 'hover' ? { scale: 0.98 } : undefined,
      transition: { duration: 0.15, ease: 'easeOut' },
      ...motionProps,
    };

    return (
      <Comp
        className={cn(enhancedButtonVariants({ variant, size, animation, className }))}
        ref={ref}
        disabled={isDisabled}
        {...(asChild ? {} : defaultMotionProps)}
        {...props}
      >
        {loading && (
          <motion.div
            className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          />
        )}
        {leftIcon && !loading && (
          <span className="flex items-center">{leftIcon}</span>
        )}
        {children}
        {rightIcon && (
          <span className="flex items-center">{rightIcon}</span>
        )}
      </Comp>
    );
  }
);

EnhancedButton.displayName = 'EnhancedButton';

/**
 * Button Group Component
 * For grouping related buttons
 */
interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: VariantProps<typeof enhancedButtonVariants>['size'];
  variant?: VariantProps<typeof enhancedButtonVariants>['variant'];
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  size,
  variant,
}) => {
  return (
    <div
      className={cn(
        'inline-flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        '[&>button]:rounded-none [&>button:first-child]:rounded-l-md [&>button:last-child]:rounded-r-md',
        orientation === 'vertical' && '[&>button:first-child]:rounded-t-md [&>button:first-child]:rounded-l-none [&>button:last-child]:rounded-b-md [&>button:last-child]:rounded-r-none',
        '[&>button:not(:first-child)]:border-l-0',
        orientation === 'vertical' && '[&>button:not(:first-child)]:border-l [&>button:not(:first-child)]:border-t-0',
        className
      )}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === EnhancedButton) {
          return React.cloneElement(child, {
            size: size || child.props.size,
            variant: variant || child.props.variant,
          } as any);
        }
        return child;
      })}
    </div>
  );
};

/**
 * Icon Button Component
 * Specialized button for icons only
 */
interface IconButtonProps extends Omit<EnhancedButtonProps, 'leftIcon' | 'rightIcon'> {
  icon: React.ReactNode;
  'aria-label': string;
}

const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'icon', ...props }, ref) => {
    return (
      <EnhancedButton
        ref={ref}
        size={size}
        className={cn('shrink-0', className)}
        {...props}
      >
        {icon}
      </EnhancedButton>
    );
  }
);

IconButton.displayName = 'IconButton';

/**
 * Loading Button Component
 * Button with built-in loading state
 */
interface LoadingButtonProps extends EnhancedButtonProps {
  loadingText?: string;
}

const LoadingButton = React.forwardRef<HTMLButtonElement, LoadingButtonProps>(
  ({ children, loadingText, loading, ...props }, ref) => {
    return (
      <EnhancedButton ref={ref} loading={loading} {...props}>
        {loading && loadingText ? loadingText : children}
      </EnhancedButton>
    );
  }
);

LoadingButton.displayName = 'LoadingButton';

/**
 * Button Utilities
 */
export const buttonUtils = {
  /**
   * Get button size classes
   */
  getSizeClasses: (size: VariantProps<typeof enhancedButtonVariants>['size']) => {
    return enhancedButtonVariants({ size });
  },
  
  /**
   * Get button variant classes
   */
  getVariantClasses: (variant: VariantProps<typeof enhancedButtonVariants>['variant']) => {
    return enhancedButtonVariants({ variant });
  },
  
  /**
   * Create custom button variant
   */
  createVariant: (baseVariant: VariantProps<typeof enhancedButtonVariants>['variant'], customClasses: string) => {
    return cn(enhancedButtonVariants({ variant: baseVariant }), customClasses);
  },
};

export { 
  EnhancedButton, 
  ButtonGroup, 
  IconButton, 
  LoadingButton, 
  enhancedButtonVariants,
  type EnhancedButtonProps,
  type ButtonGroupProps,
  type IconButtonProps,
  type LoadingButtonProps,
};
