'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@thf/ui/utils/cn';
import { StaggeredList } from '@thf/animations/layout';
import { ViewGridIcon, ViewListIcon, FilterIcon, SortIcon } from '@thf/icons/navigation';
import { EnhancedButton, IconButton } from './enhanced-button';
import { ApplicationLine, ApplicationLineSkeleton, type TeamApplication } from './application-line';

/**
 * Applications List Component
 * Supports both grid and list views with filtering and sorting
 */

interface ApplicationsListProps {
  applications?: TeamApplication[];
  loading?: boolean;
  view?: 'list' | 'grid';
  onViewChange?: (view: 'list' | 'grid') => void;
  onApplicationSelect?: (applicationId: string) => void;
  selectedApplications?: string[];
  className?: string;
}

export const ApplicationsList: React.FC<ApplicationsListProps> = ({
  applications = [],
  loading = false,
  view = 'list',
  onViewChange,
  onApplicationSelect,
  selectedApplications = [],
  className,
}) => {
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'status' | 'role'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Filter and sort applications
  const filteredAndSortedApplications = React.useMemo(() => {
    let filtered = applications;

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(app => app.status.id === filterStatus);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.fullName.localeCompare(b.fullName);
          break;
        case 'date':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'status':
          comparison = a.status.name.localeCompare(b.status.name);
          break;
        case 'role':
          comparison = a.role.localeCompare(b.role);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [applications, filterStatus, sortBy, sortOrder]);

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  if (loading) {
    return <ApplicationsListSkeleton view={view} />;
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Header with controls */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {filteredAndSortedApplications.length} applications
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Filter */}
          <EnhancedButton
            variant="outline"
            size="sm"
            leftIcon={<FilterIcon className="w-4 h-4" />}
          >
            Filter
          </EnhancedButton>
          
          {/* Sort */}
          <EnhancedButton
            variant="outline"
            size="sm"
            leftIcon={<SortIcon className="w-4 h-4" />}
          >
            Sort
          </EnhancedButton>
          
          {/* View Toggle */}
          <div className="flex items-center border border-border rounded-md">
            <IconButton
              icon={<ViewListIcon className="w-4 h-4" />}
              variant={view === 'list' ? 'default' : 'ghost'}
              size="icon-sm"
              onClick={() => onViewChange?.('list')}
              aria-label="List view"
              className="rounded-r-none border-r"
            />
            <IconButton
              icon={<ViewGridIcon className="w-4 h-4" />}
              variant={view === 'grid' ? 'default' : 'ghost'}
              size="icon-sm"
              onClick={() => onViewChange?.('grid')}
              aria-label="Grid view"
              className="rounded-l-none"
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        <AnimatePresence mode="wait">
          {view === 'list' ? (
            <ApplicationsListView
              key="list"
              applications={filteredAndSortedApplications}
              onApplicationSelect={onApplicationSelect}
              selectedApplications={selectedApplications}
              onSort={handleSort}
              sortBy={sortBy}
              sortOrder={sortOrder}
            />
          ) : (
            <ApplicationsGridView
              key="grid"
              applications={filteredAndSortedApplications}
              onApplicationSelect={onApplicationSelect}
              selectedApplications={selectedApplications}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

/**
 * List View Component
 */
interface ApplicationsListViewProps {
  applications: TeamApplication[];
  onApplicationSelect?: (applicationId: string) => void;
  selectedApplications: string[];
  onSort: (field: 'name' | 'date' | 'status' | 'role') => void;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const ApplicationsListView: React.FC<ApplicationsListViewProps> = ({
  applications,
  onApplicationSelect,
  selectedApplications,
  onSort,
  sortBy,
  sortOrder,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="divide-y divide-border"
    >
      {/* Header */}
      <div className="flex items-center px-6 py-3 bg-muted/50 text-sm font-medium text-muted-foreground">
        <div className="flex items-center gap-2 min-w-0">
          <span className="w-[66px]">ID</span>
        </div>
        <div className="min-w-0 flex-1 ml-2">
          <button
            onClick={() => onSort('name')}
            className="flex items-center gap-1 hover:text-foreground transition-colors"
          >
            Name
            {sortBy === 'name' && (
              <span className="text-xs">{sortOrder === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
        </div>
        <div className="flex items-center gap-2 ml-auto">
          <button
            onClick={() => onSort('role')}
            className="flex items-center gap-1 hover:text-foreground transition-colors"
          >
            Role
            {sortBy === 'role' && (
              <span className="text-xs">{sortOrder === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
          <button
            onClick={() => onSort('date')}
            className="flex items-center gap-1 hover:text-foreground transition-colors hidden md:flex"
          >
            Date
            {sortBy === 'date' && (
              <span className="text-xs">{sortOrder === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
          <span className="w-6" />
        </div>
      </div>

      {/* Applications */}
      <StaggeredList className="divide-y divide-border">
        {applications.map((application) => (
          <ApplicationLine
            key={application.id}
            application={application}
            layoutId={true}
            onSelect={onApplicationSelect}
            selected={selectedApplications.includes(application.id)}
            className="group"
          />
        ))}
      </StaggeredList>

      {applications.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-muted-foreground">No applications found</p>
            <p className="text-sm text-muted-foreground mt-1">
              Try adjusting your filters or search criteria
            </p>
          </div>
        </div>
      )}
    </motion.div>
  );
};

/**
 * Grid View Component
 */
interface ApplicationsGridViewProps {
  applications: TeamApplication[];
  onApplicationSelect?: (applicationId: string) => void;
  selectedApplications: string[];
}

const ApplicationsGridView: React.FC<ApplicationsGridViewProps> = ({
  applications,
  onApplicationSelect,
  selectedApplications,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="p-6"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {applications.map((application, index) => (
          <ApplicationCard
            key={application.id}
            application={application}
            onSelect={onApplicationSelect}
            selected={selectedApplications.includes(application.id)}
            delay={index * 0.05}
          />
        ))}
      </div>

      {applications.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-muted-foreground">No applications found</p>
            <p className="text-sm text-muted-foreground mt-1">
              Try adjusting your filters or search criteria
            </p>
          </div>
        </div>
      )}
    </motion.div>
  );
};

/**
 * Application Card Component (for grid view)
 */
interface ApplicationCardProps {
  application: TeamApplication;
  onSelect?: (applicationId: string) => void;
  selected: boolean;
  delay?: number;
}

const ApplicationCard: React.FC<ApplicationCardProps> = ({
  application,
  onSelect,
  selected,
  delay = 0,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      className={cn(
        'bg-card border border-border rounded-lg p-4 cursor-pointer',
        'hover:shadow-md hover:border-border/80 transition-all duration-200',
        selected && 'ring-2 ring-primary ring-offset-2'
      )}
      onClick={() => onSelect?.(application.id)}
      whileHover={{ y: -2 }}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
            {application.fullName.split(' ').map(n => n[0]).join('').slice(0, 2)}
          </div>
          <div>
            <h3 className="font-medium text-sm truncate">{application.fullName}</h3>
            <p className="text-xs text-muted-foreground">{application.identifier}</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">Role</span>
          <span className="text-xs font-medium">{application.role}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">Status</span>
          <div className="flex items-center gap-1">
            <div 
              className="w-2 h-2 rounded-full" 
              style={{ backgroundColor: application.status.color }}
            />
            <span className="text-xs">{application.status.name}</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

/**
 * Loading Skeleton
 */
const ApplicationsListSkeleton: React.FC<{ view: 'list' | 'grid' }> = ({ view }) => {
  if (view === 'grid') {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="bg-card border border-border rounded-lg p-4 animate-pulse">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-8 h-8 bg-muted rounded-full" />
                <div className="space-y-1">
                  <div className="w-20 h-3 bg-muted rounded" />
                  <div className="w-12 h-2 bg-muted rounded" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <div className="w-8 h-2 bg-muted rounded" />
                  <div className="w-12 h-2 bg-muted rounded" />
                </div>
                <div className="flex justify-between">
                  <div className="w-10 h-2 bg-muted rounded" />
                  <div className="w-16 h-2 bg-muted rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="divide-y divide-border">
      {Array.from({ length: 10 }).map((_, i) => (
        <ApplicationLineSkeleton key={i} />
      ))}
    </div>
  );
};

export { ApplicationCard, ApplicationsListView, ApplicationsGridView };
