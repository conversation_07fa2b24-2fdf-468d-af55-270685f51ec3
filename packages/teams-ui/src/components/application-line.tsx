'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { cn } from '@thf/ui/utils/cn';
import { StatusIcon } from '@thf/icons/status';
import { PriorityIcon } from '@thf/icons/priority';
import { BadgeStack } from '@thf/animations/micro';
import { MoreHorizontal, User } from 'lucide-react';
import { EnhancedButton, IconButton } from './enhanced-button';

/**
 * Application Line Component
 * Based on Circle-temp's issue line with team application context
 */

export interface TeamApplication {
  id: string;
  identifier: string;
  fullName: string;
  email: string;
  role: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  status: {
    id: string;
    name: string;
    color: string;
  };
  priority?: {
    id: string;
    name: string;
    level: number;
  };
  createdAt: string;
  updatedAt: string;
  isNdaSigned?: boolean;
  ndaSignedDate?: string;
  skills?: string[];
  avatar?: string;
}

interface ApplicationLineProps {
  application: TeamApplication;
  layoutId?: boolean;
  onStatusChange?: (applicationId: string, newStatus: string) => void;
  onPriorityChange?: (applicationId: string, newPriority: string) => void;
  onSelect?: (applicationId: string) => void;
  selected?: boolean;
  className?: string;
}

export const ApplicationLine: React.FC<ApplicationLineProps> = ({
  application,
  layoutId = false,
  onStatusChange,
  onPriorityChange,
  onSelect,
  selected = false,
  className,
}) => {
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    // Context menu logic will be implemented later
  };

  const handleClick = () => {
    onSelect?.(application.id);
  };

  return (
    <motion.div
      {...(layoutId && { layoutId: `application-line-${application.identifier}` })}
      className={cn(
        'w-full flex items-center justify-start h-11 px-6 cursor-pointer',
        'hover:bg-sidebar/50 transition-colors duration-150',
        'border-b border-border/50 last:border-b-0',
        selected && 'bg-accent/50',
        className
      )}
      onClick={handleClick}
      onContextMenu={handleContextMenu}
      whileHover={{ backgroundColor: 'rgba(var(--sidebar), 0.5)' }}
      transition={{ duration: 0.15 }}
    >
      {/* Status and Identifier */}
      <div className="flex items-center gap-2 min-w-0">
        <StatusIcon 
          statusId={application.status.id} 
          type="application"
          className="w-3.5 h-3.5 shrink-0"
        />
        <span className="text-sm text-muted-foreground font-medium w-[66px] truncate">
          {application.identifier}
        </span>
      </div>

      {/* Name and Email */}
      <div className="min-w-0 flex-1 flex items-center justify-start mr-1 ml-2">
        <div className="min-w-0 flex-1">
          <span className="text-xs sm:text-sm font-medium sm:font-semibold truncate block">
            {application.fullName}
          </span>
          <span className="text-xs text-muted-foreground truncate block">
            {application.email}
          </span>
        </div>
      </div>

      {/* Badges and Metadata */}
      <div className="flex items-center justify-end gap-2 ml-auto">
        {/* Priority */}
        {application.priority && (
          <PriorityIcon 
            priorityId={application.priority.id}
            className="w-4 h-4 shrink-0"
          />
        )}

        {/* Role Badge */}
        <RoleBadge role={application.role} />

        {/* Skills Badges */}
        {application.skills && application.skills.length > 0 && (
          <BadgeStack className="hidden sm:flex">
            {application.skills.slice(0, 3).map((skill, index) => (
              <SkillBadge key={skill} skill={skill} index={index} />
            ))}
            {application.skills.length > 3 && (
              <div className="bg-muted text-muted-foreground text-xs px-2 py-1 rounded-full border border-border">
                +{application.skills.length - 3}
              </div>
            )}
          </BadgeStack>
        )}

        {/* NDA Status */}
        {application.isNdaSigned && (
          <div className="w-2 h-2 bg-green-500 rounded-full shrink-0" title="NDA Signed" />
        )}

        {/* Date */}
        <span className="text-xs text-muted-foreground hidden md:block">
          {format(new Date(application.createdAt), 'MMM dd')}
        </span>

        {/* Avatar */}
        <UserAvatar user={application} />

        {/* Actions */}
        <IconButton
          icon={<MoreHorizontal className="w-4 h-4" />}
          variant="ghost"
          size="icon-xs"
          aria-label="More actions"
          className="opacity-0 group-hover:opacity-100 transition-opacity"
        />
      </div>
    </motion.div>
  );
};

/**
 * Role Badge Component
 */
interface RoleBadgeProps {
  role: TeamApplication['role'];
  className?: string;
}

const RoleBadge: React.FC<RoleBadgeProps> = ({ role, className }) => {
  const roleColors = {
    Admin: 'bg-purple-100 text-purple-800 border-purple-200',
    Collaborator: 'bg-blue-100 text-blue-800 border-blue-200',
    Affiliate: 'bg-green-100 text-green-800 border-green-200',
    Volunteer: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  };

  return (
    <span
      className={cn(
        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border',
        roleColors[role],
        className
      )}
    >
      {role}
    </span>
  );
};

/**
 * Skill Badge Component
 */
interface SkillBadgeProps {
  skill: string;
  index: number;
  className?: string;
}

const SkillBadge: React.FC<SkillBadgeProps> = ({ skill, index, className }) => {
  return (
    <motion.span
      className={cn(
        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
        'bg-secondary text-secondary-foreground border border-border',
        className
      )}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2, delay: index * 0.05 }}
    >
      {skill}
    </motion.span>
  );
};

/**
 * User Avatar Component
 */
interface UserAvatarProps {
  user: Pick<TeamApplication, 'fullName' | 'avatar'>;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const UserAvatar: React.FC<UserAvatarProps> = ({ 
  user, 
  size = 'sm',
  className 
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base',
  };

  const initials = user.fullName
    .split(' ')
    .map(name => name[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <div
      className={cn(
        'rounded-full bg-primary text-primary-foreground flex items-center justify-center font-medium shrink-0',
        sizeClasses[size],
        className
      )}
    >
      {user.avatar ? (
        <img
          src={user.avatar}
          alt={user.fullName}
          className="w-full h-full rounded-full object-cover"
        />
      ) : (
        <span>{initials}</span>
      )}
    </div>
  );
};

/**
 * Application Line Skeleton
 * Loading state for application lines
 */
export const ApplicationLineSkeleton: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('w-full flex items-center justify-start h-11 px-6', className)}>
      <div className="flex items-center gap-2">
        <div className="w-3.5 h-3.5 bg-muted rounded-full animate-pulse" />
        <div className="w-16 h-4 bg-muted rounded animate-pulse" />
      </div>
      
      <div className="min-w-0 flex-1 flex items-center justify-start mr-1 ml-2">
        <div className="min-w-0 flex-1 space-y-1">
          <div className="w-32 h-4 bg-muted rounded animate-pulse" />
          <div className="w-24 h-3 bg-muted rounded animate-pulse" />
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <div className="w-16 h-6 bg-muted rounded-full animate-pulse" />
        <div className="w-12 h-3 bg-muted rounded animate-pulse" />
        <div className="w-6 h-6 bg-muted rounded-full animate-pulse" />
      </div>
    </div>
  );
};

export { RoleBadge, SkillBadge, UserAvatar };
