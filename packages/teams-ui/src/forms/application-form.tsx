'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { cn } from '@thf/ui/utils/cn';
import { EnhancedButton, LoadingButton } from '../components/enhanced-button';
import { StaggerChildren } from '@thf/animations/micro';
import { User, Mail, Briefcase, FileText, Star } from 'lucide-react';

/**
 * Application Form Schema
 */
const applicationFormSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  role: z.enum(['Collaborator', 'Affiliate', 'Volunteer'], {
    required_error: 'Please select a role',
  }),
  experience: z.string().min(10, 'Please provide at least 10 characters of experience'),
  skills: z.array(z.string()).min(1, 'Please select at least one skill'),
  portfolio: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  motivation: z.string().min(50, 'Please provide at least 50 characters explaining your motivation'),
  availability: z.enum(['full-time', 'part-time', 'project-based'], {
    required_error: 'Please select your availability',
  }),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms'),
});

type ApplicationFormData = z.infer<typeof applicationFormSchema>;

/**
 * Application Form Component
 */
interface ApplicationFormProps {
  onSubmit: (data: ApplicationFormData) => Promise<void>;
  loading?: boolean;
  className?: string;
}

export const ApplicationForm: React.FC<ApplicationFormProps> = ({
  onSubmit,
  loading = false,
  className,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      skills: [],
      agreeToTerms: false,
    },
  });

  const selectedRole = watch('role');
  const selectedSkills = watch('skills');

  const handleFormSubmit = async (data: ApplicationFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const availableSkills = [
    'React', 'TypeScript', 'Node.js', 'Python', 'Design', 'Marketing',
    'Content Writing', 'Project Management', 'Data Analysis', 'UI/UX',
    'Photography', 'Video Editing', 'Social Media', 'SEO', 'Copywriting',
  ];

  const toggleSkill = (skill: string) => {
    const currentSkills = selectedSkills || [];
    const newSkills = currentSkills.includes(skill)
      ? currentSkills.filter(s => s !== skill)
      : [...currentSkills, skill];
    setValue('skills', newSkills);
  };

  return (
    <motion.form
      onSubmit={handleSubmit(handleFormSubmit)}
      className={cn('space-y-8 max-w-2xl mx-auto', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <StaggerChildren staggerDelay={0.1}>
        {/* Personal Information */}
        <FormSection
          title="Personal Information"
          description="Tell us about yourself"
          icon={<User className="w-5 h-5" />}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Full Name"
              error={errors.fullName?.message}
              required
            >
              <input
                {...register('fullName')}
                type="text"
                className="form-input"
                placeholder="Enter your full name"
              />
            </FormField>

            <FormField
              label="Email Address"
              error={errors.email?.message}
              required
            >
              <input
                {...register('email')}
                type="email"
                className="form-input"
                placeholder="Enter your email address"
              />
            </FormField>
          </div>
        </FormSection>

        {/* Role Selection */}
        <FormSection
          title="Role & Availability"
          description="What role are you interested in?"
          icon={<Briefcase className="w-5 h-5" />}
        >
          <div className="space-y-6">
            <FormField
              label="Desired Role"
              error={errors.role?.message}
              required
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {(['Collaborator', 'Affiliate', 'Volunteer'] as const).map((role) => (
                  <RoleCard
                    key={role}
                    role={role}
                    selected={selectedRole === role}
                    onSelect={() => setValue('role', role)}
                  />
                ))}
              </div>
            </FormField>

            <FormField
              label="Availability"
              error={errors.availability?.message}
              required
            >
              <select {...register('availability')} className="form-select">
                <option value="">Select availability</option>
                <option value="full-time">Full-time</option>
                <option value="part-time">Part-time</option>
                <option value="project-based">Project-based</option>
              </select>
            </FormField>
          </div>
        </FormSection>

        {/* Skills & Experience */}
        <FormSection
          title="Skills & Experience"
          description="What skills and experience do you bring?"
          icon={<Star className="w-5 h-5" />}
        >
          <div className="space-y-6">
            <FormField
              label="Skills"
              error={errors.skills?.message}
              required
            >
              <div className="flex flex-wrap gap-2">
                {availableSkills.map((skill) => (
                  <SkillTag
                    key={skill}
                    skill={skill}
                    selected={selectedSkills?.includes(skill) || false}
                    onToggle={() => toggleSkill(skill)}
                  />
                ))}
              </div>
            </FormField>

            <FormField
              label="Experience"
              error={errors.experience?.message}
              required
            >
              <textarea
                {...register('experience')}
                rows={4}
                className="form-textarea"
                placeholder="Describe your relevant experience..."
              />
            </FormField>

            <FormField
              label="Portfolio/Website"
              error={errors.portfolio?.message}
            >
              <input
                {...register('portfolio')}
                type="url"
                className="form-input"
                placeholder="https://your-portfolio.com"
              />
            </FormField>
          </div>
        </FormSection>

        {/* Motivation */}
        <FormSection
          title="Motivation"
          description="Why do you want to join The Hue Factory?"
          icon={<FileText className="w-5 h-5" />}
        >
          <FormField
            label="Tell us why you're interested"
            error={errors.motivation?.message}
            required
          >
            <textarea
              {...register('motivation')}
              rows={6}
              className="form-textarea"
              placeholder="Share your motivation for joining our team..."
            />
          </FormField>
        </FormSection>

        {/* Terms Agreement */}
        <FormSection>
          <FormField error={errors.agreeToTerms?.message}>
            <label className="flex items-start gap-3 cursor-pointer">
              <input
                {...register('agreeToTerms')}
                type="checkbox"
                className="form-checkbox mt-1"
              />
              <span className="text-sm text-muted-foreground">
                I agree to the{' '}
                <a href="/terms" className="text-primary hover:underline">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="/privacy" className="text-primary hover:underline">
                  Privacy Policy
                </a>
                , and I understand that my application will be reviewed by the team.
              </span>
            </label>
          </FormField>
        </FormSection>

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <LoadingButton
            type="submit"
            loading={isSubmitting || loading}
            loadingText="Submitting Application..."
            size="lg"
            className="min-w-[200px]"
          >
            Submit Application
          </LoadingButton>
        </div>
      </StaggerChildren>
    </motion.form>
  );
};

/**
 * Form Section Component
 */
interface FormSectionProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  icon,
  children,
}) => {
  return (
    <div className="space-y-6">
      {title && (
        <div className="flex items-center gap-3">
          {icon && (
            <div className="w-10 h-10 bg-primary/10 text-primary rounded-lg flex items-center justify-center">
              {icon}
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>
        </div>
      )}
      {children}
    </div>
  );
};

/**
 * Form Field Component
 */
interface FormFieldProps {
  label?: string;
  error?: string;
  required?: boolean;
  children: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  required,
  children,
}) => {
  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium text-foreground">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      {children}
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm text-destructive"
        >
          {error}
        </motion.p>
      )}
    </div>
  );
};

/**
 * Role Card Component
 */
interface RoleCardProps {
  role: 'Collaborator' | 'Affiliate' | 'Volunteer';
  selected: boolean;
  onSelect: () => void;
}

const RoleCard: React.FC<RoleCardProps> = ({ role, selected, onSelect }) => {
  const roleDescriptions = {
    Collaborator: 'Full team member with ongoing responsibilities',
    Affiliate: 'Partnership opportunities and project collaboration',
    Volunteer: 'Contribute to specific projects and initiatives',
  };

  return (
    <motion.div
      className={cn(
        'p-4 border rounded-lg cursor-pointer transition-all duration-200',
        selected
          ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
          : 'border-border hover:border-border/80 hover:bg-accent/50'
      )}
      onClick={onSelect}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <h4 className="font-medium text-foreground">{role}</h4>
      <p className="text-xs text-muted-foreground mt-1">
        {roleDescriptions[role]}
      </p>
    </motion.div>
  );
};

/**
 * Skill Tag Component
 */
interface SkillTagProps {
  skill: string;
  selected: boolean;
  onToggle: () => void;
}

const SkillTag: React.FC<SkillTagProps> = ({ skill, selected, onToggle }) => {
  return (
    <motion.button
      type="button"
      className={cn(
        'px-3 py-1 rounded-full text-sm font-medium transition-all duration-200',
        selected
          ? 'bg-primary text-primary-foreground'
          : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
      )}
      onClick={onToggle}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {skill}
    </motion.button>
  );
};

export { FormSection, FormField, RoleCard, SkillTag };
