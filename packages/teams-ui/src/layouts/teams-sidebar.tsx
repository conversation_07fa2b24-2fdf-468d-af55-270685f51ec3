'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@thf/ui/utils/cn';
import { EnhancedButton, IconButton } from '../components/enhanced-button';
import { NavigationIcon, mainNavigation, utilityNavigation } from '@thf/icons/navigation';
import { ThemeToggle } from '@thf/theme/theme-provider';
import { ChevronLeft, ChevronRight, Search, Plus } from 'lucide-react';

/**
 * Teams Sidebar Component
 * Based on Circle-temp's sophisticated sidebar with collapsible behavior
 */

interface TeamsSidebarProps {
  className?: string;
  defaultCollapsed?: boolean;
  onCollapsedChange?: (collapsed: boolean) => void;
}

export const TeamsSidebar: React.FC<TeamsSidebarProps> = ({
  className,
  defaultCollapsed = false,
  onCollapsedChange,
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [activeItem, setActiveItem] = useState('dashboard');

  const handleCollapsedChange = (newCollapsed: boolean) => {
    setCollapsed(newCollapsed);
    onCollapsedChange?.(newCollapsed);
  };

  return (
    <motion.div
      className={cn(
        'flex flex-col h-full bg-sidebar border-r border-sidebar-border',
        className
      )}
      animate={{
        width: collapsed ? 48 : 256,
      }}
      transition={{
        duration: 0.2,
        ease: [0.4, 0, 0.2, 1],
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-sidebar-border">
        <AnimatePresence mode="wait">
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.15 }}
              className="flex items-center gap-2"
            >
              <div className="w-8 h-8 bg-sidebar-primary rounded-md flex items-center justify-center">
                <span className="text-sidebar-primary-foreground font-bold text-sm">T</span>
              </div>
              <span className="font-semibold text-sidebar-foreground">Teams</span>
            </motion.div>
          )}
        </AnimatePresence>
        
        <IconButton
          icon={collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          variant="ghost"
          size="icon-sm"
          onClick={() => handleCollapsedChange(!collapsed)}
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          className="text-sidebar-foreground hover:bg-sidebar-accent"
        />
      </div>

      {/* Search */}
      {!collapsed && (
        <div className="p-3 border-b border-sidebar-border">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.1 }}
          >
            <EnhancedButton
              variant="outline"
              size="sm"
              leftIcon={<Search className="w-4 h-4" />}
              className="w-full justify-start text-muted-foreground border-sidebar-border hover:bg-sidebar-accent"
            >
              Search...
            </EnhancedButton>
          </motion.div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto p-2">
        <div className="space-y-1">
          {/* Main Navigation */}
          <div className="space-y-1">
            {mainNavigation.map((item, index) => (
              <SidebarNavItem
                key={item.id}
                item={item}
                collapsed={collapsed}
                active={activeItem === item.id}
                onClick={() => setActiveItem(item.id)}
                delay={index * 0.05}
              />
            ))}
          </div>

          {/* Divider */}
          <div className="my-4">
            <div className="h-px bg-sidebar-border" />
          </div>

          {/* Utility Navigation */}
          <div className="space-y-1">
            {utilityNavigation.map((item, index) => (
              <SidebarNavItem
                key={item.id}
                item={item}
                collapsed={collapsed}
                active={activeItem === item.id}
                onClick={() => setActiveItem(item.id)}
                delay={(mainNavigation.length + index) * 0.05}
              />
            ))}
          </div>
        </div>
      </nav>

      {/* Footer */}
      <div className="p-3 border-t border-sidebar-border">
        <AnimatePresence mode="wait">
          {!collapsed ? (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.15 }}
              className="space-y-2"
            >
              <EnhancedButton
                variant="default"
                size="sm"
                leftIcon={<Plus className="w-4 h-4" />}
                className="w-full"
              >
                New Application
              </EnhancedButton>
              
              <div className="flex items-center justify-between">
                <ThemeToggle className="text-sidebar-foreground" />
                <div className="flex items-center gap-1">
                  <div className="w-6 h-6 bg-sidebar-primary rounded-full flex items-center justify-center">
                    <span className="text-xs text-sidebar-primary-foreground font-medium">A</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.15 }}
              className="flex flex-col items-center gap-2"
            >
              <ThemeToggle className="text-sidebar-foreground" />
              <div className="w-6 h-6 bg-sidebar-primary rounded-full flex items-center justify-center">
                <span className="text-xs text-sidebar-primary-foreground font-medium">A</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

/**
 * Sidebar Navigation Item Component
 */
interface SidebarNavItemProps {
  item: {
    id: string;
    name: string;
    icon: React.FC<any>;
    href: string;
    badge?: number;
  };
  collapsed: boolean;
  active: boolean;
  onClick: () => void;
  delay?: number;
}

const SidebarNavItem: React.FC<SidebarNavItemProps> = ({
  item,
  collapsed,
  active,
  onClick,
  delay = 0,
}) => {
  const IconComponent = item.icon;

  return (
    <motion.div
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2, delay }}
    >
      <EnhancedButton
        variant={active ? 'secondary' : 'ghost'}
        size="sm"
        onClick={onClick}
        className={cn(
          'w-full justify-start gap-3 text-sidebar-foreground',
          active 
            ? 'bg-sidebar-accent text-sidebar-accent-foreground' 
            : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',
          collapsed && 'justify-center px-2'
        )}
        leftIcon={<IconComponent className="w-4 h-4" />}
      >
        <AnimatePresence mode="wait">
          {!collapsed && (
            <motion.span
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.15 }}
              className="truncate"
            >
              {item.name}
            </motion.span>
          )}
        </AnimatePresence>
        
        {item.badge && !collapsed && (
          <motion.span
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="ml-auto bg-sidebar-primary text-sidebar-primary-foreground text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center"
          >
            {item.badge}
          </motion.span>
        )}
      </EnhancedButton>
    </motion.div>
  );
};

/**
 * Sidebar Utilities
 */
export const sidebarUtils = {
  /**
   * Get sidebar width based on collapsed state
   */
  getSidebarWidth: (collapsed: boolean) => collapsed ? 48 : 256,
  
  /**
   * Calculate main content margin based on sidebar state
   */
  getMainContentMargin: (collapsed: boolean, showSidebar: boolean) => {
    if (!showSidebar) return 0;
    return collapsed ? 48 : 256;
  },
  
  /**
   * Get responsive sidebar classes
   */
  getResponsiveSidebarClasses: (collapsed: boolean) => {
    return cn(
      'transition-all duration-200 ease-in-out',
      collapsed ? 'w-12' : 'w-64',
      'hidden lg:flex lg:flex-col'
    );
  },
};

export { SidebarNavItem };
