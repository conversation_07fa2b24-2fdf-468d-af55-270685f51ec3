'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@thf/ui/utils/cn';
import { PageTransition } from '@thf/animations/layout';

/**
 * Main Layout Component
 * Based on Circle-temp's sophisticated layout system with responsive design
 */

interface MainLayoutProps {
  children: React.ReactNode;
  header?: React.ReactNode;
  sidebar?: React.ReactNode;
  headersNumber?: 1 | 2;
  className?: string;
  sidebarCollapsed?: boolean;
  showSidebar?: boolean;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  header,
  sidebar,
  headersNumber = 2,
  className,
  sidebarCollapsed = false,
  showSidebar = true,
}) => {
  // Calculate height based on number of headers
  const contentHeight = {
    1: 'h-[calc(100svh-40px)] lg:h-[calc(100svh-56px)]',
    2: 'h-[calc(100svh-80px)] lg:h-[calc(100svh-96px)]',
  };

  return (
    <div className="h-svh overflow-hidden lg:p-2 w-full">
      <div className="lg:border lg:rounded-md overflow-hidden flex bg-container h-full w-full">
        {/* Sidebar */}
        {showSidebar && (
          <motion.aside
            className={cn(
              'shrink-0 border-r border-sidebar-border bg-sidebar',
              sidebarCollapsed ? 'w-12' : 'w-64',
              'hidden lg:flex lg:flex-col'
            )}
            animate={{
              width: sidebarCollapsed ? 48 : 256,
            }}
            transition={{
              duration: 0.2,
              ease: [0.4, 0, 0.2, 1],
            }}
          >
            {sidebar}
          </motion.aside>
        )}

        {/* Main Content Area */}
        <div className="flex flex-col flex-1 min-w-0">
          {/* Header */}
          {header && (
            <motion.header
              className="shrink-0 border-b border-border bg-background"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            >
              {header}
            </motion.header>
          )}

          {/* Content */}
          <main
            className={cn(
              'overflow-auto w-full bg-background',
              contentHeight[headersNumber],
              className
            )}
          >
            <PageTransition>
              {children}
            </PageTransition>
          </main>
        </div>
      </div>
    </div>
  );
};

/**
 * Layout Header Component
 * Standardized header with consistent styling
 */
interface LayoutHeaderProps {
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  breadcrumbs?: React.ReactNode;
  className?: string;
  children?: React.ReactNode;
}

export const LayoutHeader: React.FC<LayoutHeaderProps> = ({
  title,
  subtitle,
  actions,
  breadcrumbs,
  className,
  children,
}) => {
  return (
    <div className={cn('px-6 py-4 border-b border-border', className)}>
      {breadcrumbs && (
        <div className="mb-2">
          {breadcrumbs}
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <div className="min-w-0 flex-1">
          {title && (
            <h1 className="text-lg font-semibold leading-tight text-foreground truncate">
              {title}
            </h1>
          )}
          {subtitle && (
            <p className="text-sm text-muted-foreground mt-1">
              {subtitle}
            </p>
          )}
          {children}
        </div>
        
        {actions && (
          <div className="flex items-center gap-2 ml-4">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Layout Content Component
 * Standardized content area with consistent padding
 */
interface LayoutContentProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export const LayoutContent: React.FC<LayoutContentProps> = ({
  children,
  className,
  padding = 'md',
}) => {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  return (
    <div className={cn(paddingClasses[padding], className)}>
      {children}
    </div>
  );
};

/**
 * Layout Grid Component
 * Responsive grid system for layout content
 */
interface LayoutGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const LayoutGrid: React.FC<LayoutGridProps> = ({
  children,
  columns = 1,
  gap = 'md',
  className,
}) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
    12: 'grid-cols-12',
  };

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  return (
    <div className={cn('grid', gridClasses[columns], gapClasses[gap], className)}>
      {children}
    </div>
  );
};

/**
 * Layout Card Component
 * Standardized card container for layout sections
 */
interface LayoutCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export const LayoutCard: React.FC<LayoutCardProps> = ({
  children,
  title,
  description,
  actions,
  className,
  padding = 'md',
}) => {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  return (
    <motion.div
      className={cn(
        'bg-card border border-border rounded-lg shadow-xs',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
    >
      {(title || description || actions) && (
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h3 className="text-base font-semibold text-card-foreground">
                  {title}
                </h3>
              )}
              {description && (
                <p className="text-sm text-muted-foreground mt-1">
                  {description}
                </p>
              )}
            </div>
            {actions && (
              <div className="flex items-center gap-2">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className={paddingClasses[padding]}>
        {children}
      </div>
    </motion.div>
  );
};

/**
 * Layout Utilities
 */
export const layoutUtils = {
  /**
   * Get responsive breakpoint classes
   */
  getBreakpointClasses: (breakpoint: 'sm' | 'md' | 'lg' | 'xl' | '2xl') => {
    const breakpoints = {
      sm: 'sm:',
      md: 'md:',
      lg: 'lg:',
      xl: 'xl:',
      '2xl': '2xl:',
    };
    return breakpoints[breakpoint];
  },
  
  /**
   * Create responsive grid columns
   */
  createResponsiveGrid: (mobile: number, tablet: number, desktop: number) => {
    return `grid-cols-${mobile} md:grid-cols-${tablet} lg:grid-cols-${desktop}`;
  },
  
  /**
   * Get container max width classes
   */
  getContainerClasses: (size: 'sm' | 'md' | 'lg' | 'xl' | 'full') => {
    const containers = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      full: 'max-w-full',
    };
    return containers[size];
  },
};
