{"name": "with-tailwind", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "check-types": "turbo run check-types", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "turbo": "^2.5.4"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.15", "workspaces": ["apps/*", "packages/*"]}